<script setup lang="ts">
import { getAssetsImages } from '@/utils'
import tuiUpload from '@/components/thorui/tui-upload/tui-upload.vue'

interface Upload {
  isSfz:boolean
  sfz_z:boolean
}
const props = withDefaults(defineProps<Upload>(), {
  isSfz:false,
  sfz_z:true
})
const value = defineModel<string[]>()

const serverUrl = '/adminapi/common/upload'

// 上传图片
const uploadComplete = (data: any) => {
  const { status, imgArr } = data
  if (status === 1) {
    value.value = imgArr
  }
}
</script>

<template>
  <tuiUpload
    v-bind="$attrs"
    :width="isSfz?240:190"
    :height="isSfz?152:190"
    ref="uploadRef"
    :serverUrl="serverUrl"
    :value="value"
    @complete="uploadComplete"
  >
    <template #default  v-if="isSfz">
      <image
        :src="sfz_z?getAssetsImages('sfz1.png'):getAssetsImages('sfz2.png')"
        mode="scaleToFill"
        style="width: 240rpx;height:152rpx;"
      />
    </template>
  </tuiUpload>
</template>

<style scoped></style>
