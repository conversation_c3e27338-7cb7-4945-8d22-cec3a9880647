export interface FooterButtonProps {
  /**
   * @description 是否固定在底部
   */
  bottom?: boolean
  /**
   * @description 在固定后是否开启占位
   */
  placeholder?: boolean
  /**
   * @deprecated 是否显示阴影
   */
  boxShadow?: boolean
}

export interface FooterButtonEmits {
  /**
   * @description: 参考官方按钮 getuserinfo(返回获取到的用户信息)
   */
  (e: 'getuserinfo'): void

  /**
   * @description 打开客服会话，参考官方按钮open-type值
   */
  (e: 'contact', event: any): void

  /**
   * @description 获取用户手机号回调，参考官方按钮getphonenumber
   */
  (e: 'getphonenumber', event: any): void

  /**
   * @description 当使用开放能力时，发生错误的回调，参考官方按钮error
   */
  (e: 'error', event: any): void

  /**
   * @description 按钮点击事件，设置formType时无需使用
   */
  (e: 'click'): void

  /**
   * @description 获取用户信息回调
   */
  (e: 'getuserinfo', event: any): void

  /**
   * @description 获取用户头像回调
   */
  (e: 'chooseavatar', event: any): void

  /**
   * @description 从小程序打开 App 成功的回调
   */
  (e: 'launchapp', event: any): void
}
