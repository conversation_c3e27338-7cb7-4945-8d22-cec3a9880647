<script lang="ts" setup>
import type { FooterButtonEmits, FooterButtonProps } from './types'

const props = withDefaults(defineProps<FooterButtonProps>(), {
  bottom: true,
  placeholder: true,
  boxShadow: false,
})
const emits = defineEmits<FooterButtonEmits>()

const { getBoundingClientRect } = useSelectorQuery()
const { safeAreaInsetBottom } = useSystemRectInfo()

const height = ref('auto')

onMounted(async () => {
  const { height: containerHeight } = await getBoundingClientRect('.tq-footer-button')
  height.value = `${containerHeight}px`
})
</script>

// #ifdef MP-WEIXIN
<script lang="ts">
export default {
  options: {
    // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现(不会出现shadow节点下再去创建元素)
    virtualHost: true,
  },
}
</script>
// #endif

<template>
  <view :style="{ height }" class="tq-footer-button--placeholder">
    <view
      :class="props.boxShadow && 'box-shadow'"
      :style="{
        position: bottom ? 'fixed' : 'relative',
        paddingBottom: safeAreaInsetBottom ? `${safeAreaInsetBottom}px` : '32rpx',
      }"
      class="tq-footer-button"
    >
      <tui-form-button height="92rpx" radius="8rpx" v-bind="$attrs" @click="emits('click')">
        <slot />
      </tui-form-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.tq-footer-button {
  position: fixed;
  padding: 32rpx 40rpx 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 999;
}

.box-shadow {
  box-shadow: 0px -4px 5px 0px rgba(153, 153, 153, 0.13);
}
</style>
