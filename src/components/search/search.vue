<script lang="ts" setup>
import { isString, addStyle } from '@/utils'
import { debounce } from 'lodash-es'
import type { CSSProperties } from 'vue'
import type { InputOnInputEvent } from '@uni-helper/uni-app-types'
import type { SearchEmits, SearchProps } from './types.ts'

const props = withDefaults(defineProps<SearchProps>(), {
  placeholder: '请输入搜索内容',
  disabled: false,
  debounceTime: 500,
})
const emits = defineEmits<SearchEmits>()

const searchValue = defineModel<string>({ default: '' })

const searchStyle = computed<CSSProperties>(() => {
  return {
    ...(isString(props.searchStyle) ? (addStyle(props.searchStyle) as CSSProperties) : props.searchStyle),
  }
})

// 输入框输入事件
const inputHandle = (e: InputOnInputEvent) => {
  searchValue.value = e.detail.value

  nextTick(() => {
    emits('input', e.detail.value)
  })
}
const inputValueEvent = props.debounce ? debounce(inputHandle, props.debounceTime) : inputHandle
</script>

<template>
  <view :class="searchClass" :style="searchStyle" class="flex items-center px-2.5 h-9 rounded-full bg-white">
    <view class="i-icon-park-outline:search mr-2 font-700 text-base text-[#999]"></view>
    <input
      :focus="focus"
      :placeholder="placeholder"
      :value="searchValue"
      class="flex-1"
      confirm-type="search"
      @input="inputValueEvent"
    />
  </view>
</template>

<style lang="scss" scoped>
.input-placeholder {
  font-weight: 350;
  font-size: 28rpx;
  color: #999;
}
</style>
