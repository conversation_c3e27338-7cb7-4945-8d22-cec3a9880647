export interface SearchProps {
  /**
   * @description: 搜索框占位内容的值
   */
  placeholder?: string
  /**
   * @description: 获取搜索框焦点
   */
  focus?: boolean
  /**
   * @description: 是否显示搜索按钮
   */
  searchButton?: boolean
  /**
   * @description: 搜索框样式类
   */
  searchClass?: string
  /**
   * @description: 搜索框样式
   */
  searchStyle?: string | Record<string, string>
  /**
   * @description: 搜索是否防抖
   */
  debounce?: boolean
  /**
   * @description: 防抖延迟时间，单位毫秒
   */
  debounceTime?: number
}

export interface SearchEmits {
  /**
   * @description: 输入时触发
   */
  (e: 'input', value: string): void

  /**
   * @description: 点击搜索按钮时触发
   */
  (e: 'search', value: string): void

  /**
   * @description: 聚焦搜索输入框框时触发
   */
  (e: 'focus'): void

  /**
   * @description: 失去焦点时触发
   */
  (e: 'blur'): void
}
