<script lang="ts" setup>
import type { ModalSuccessProps } from './types.ts'

withDefaults(defineProps<ModalSuccessProps>(), {
  showClose: true,
  confirmText: '确定',
  cancelText: '取消',
  showCancel: true,
})
const show = defineModel({ type: Boolean, default: false })

const emit = defineEmits(['cancel', 'confirm', 'close'])

const handleCancel = () => {
  show.value = false
  emit('cancel')
}

const handleConfirm = () => {
  show.value = false
  emit('confirm')
}

const handleClose = () => {
  show.value = false
  emit('close')
}
</script>

<template>
  <tui-landscape :close-icon="false" :show="show">
    <tui-popup :mode-class="['zoom-in']" :show="show" :styles="{ top: '-180rpx', zIndex: 999999 }">
      <view class="">
        <view class="modal-content bg-image pt-37.5 flex flex-col items-center">
          <view class="mt-2 text-xl font-700 text-black">{{ title }}</view>
          <view class="px-7 py-5 text-base text-center text-black">
            <slot name="content">
              {{ content }}
            </slot>
          </view>
          <view class="flex items-center justify-center gap-x-3.5 mt-a mb-8 w-full px-10">
            <slot name="bottom">
              <tui-form-button
                v-if="showCancel"
                color="#F55545"
                height="80rpx"
                plain
                width="240rpx"
                @click="handleCancel"
              >
                {{ cancelText }}
              </tui-form-button>
              <tui-form-button color="#fff" height="80rpx" width="240rpx" @click="handleConfirm">
                {{ confirmText }}
              </tui-form-button>
            </slot>
          </view>
        </view>
        <view v-if="showClose" class="flex justify-center mt-3">
          <tui-icon color="#fff" name="close" @click="handleClose" />
        </view>
      </view>
    </tui-popup>
  </tui-landscape>
</template>

<style lang="scss" scoped>
.modal-content {
  width: 636rpx;
  height: 704rpx;
  background-image: url('@/static/images/modal_success.png');
}
</style>
