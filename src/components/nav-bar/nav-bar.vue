<script lang="ts" setup>
import type { CSSProperties } from 'vue'
import type { NavBarProps } from './types'

const props = withDefaults(defineProps<NavBarProps>(), {
  fixed: true,
  placeholder: true,
  bgColor: '#fff',
  textColor: '#333',
  backIcon: 'arrowleft',
  showBack: true,
  zIndex: 99998,
})

const { navBarInfo } = useSystemRectInfo()

// 导航栏样式
const navBarStyle = computed<CSSProperties>(() => {
  return {
    height: `${navBarInfo.height}px`,
    position: props.fixed ? 'fixed' : 'relative',
    backgroundColor: props.bgColor || '#fff',
    color: props.textColor || '#333',
    zIndex: props.zIndex,
  }
})

// 导航栏容器区域样式
const navBarWrapperStyle = computed<CSSProperties>(() => {
  return {
    top: `${navBarInfo.statusHeight}px`,
    height: `${navBarInfo.height - navBarInfo.statusHeight}px`,
  }
})

// 导航栏占位样式
const navbarPlaceholderStyle = computed<CSSProperties>(() => {
  return {
    height: `${navBarInfo.height}px`,
  }
})

const handleBackClick = () => {
  uni.navigateBack()
}
</script>

// #ifdef MP-WEIXIN
<script lang="ts">
export default {
  options: {
    // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现(不会出现shadow节点下再去创建元素)
    virtualHost: true,
  },
}
</script>
// #endif

<template>
  <view>
    <view :style="navBarStyle" class="top-0 left-0 right-0">
      <view :style="navBarWrapperStyle" class="flex items-center gap-x-4 relative px-3">
        <view class="flex-1">
          <slot name="back">
            <tui-icon v-if="showBack" :color="textColor" :name="backIcon" :size="24" @click="handleBackClick" />
          </slot>
        </view>
        <view class="min-w-15 flex justify-center text-base">
          <slot />
        </view>
        <view class="flex-1" />
      </view>
    </view>
    <!-- 固定之后会导致容器塌陷 -->
    <view v-if="fixed && placeholder" :style="navbarPlaceholderStyle" />
  </view>
</template>

<style lang="scss" scoped></style>
