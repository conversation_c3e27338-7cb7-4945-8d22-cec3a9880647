<script lang="ts" setup>
import { getAssetsImages } from '@/utils'
import { VoteProgressEnum } from '@/api/ticket/types'
import type { VoteCardProps } from './types.ts'

const props = defineProps<VoteCardProps>()

const statusStyle = computed(() => {
  switch (props.status) {
    case VoteProgressEnum.UN_START:
      return {
        text: '未开始',
        textColor: '#fff',
        bgColor: '#999',
      }
    case VoteProgressEnum.IN_PROGRESS:
      return {
        text: '进行中',
        textColor: '#fff',
        bgColor: '#00B384',
      }
    case VoteProgressEnum.FINISHED:
      return {
        text: '已结束',
        textColor: '#fff',
        bgColor: '#999',
      }
  }
})
</script>

<template>
  <view class="bg-white px-3.5">
    <view class="flex gap-x-3 py-3.5 bor-b relative">
      <!-- 业主大会图片 -->
      <view class="relative shrink-0 size-22 rounded overflow-hidden">
        <!-- 状态标签 -->
        <view
          class="absolute top-0 left-0 px-1 h-4.5 bg-[#999] rounded-br-md z-1 text-white text-xs leading-4.5"
          :style="{
            backgroundColor: statusStyle?.bgColor,
            color: statusStyle?.textColor,
          }"
        >
          {{ statusStyle?.text }}
        </view>

        <image class="size-full" mode="aspectFill" src="@/static/images/vote_image.png" />
      </view>

      <image
        v-if="isVoted !== undefined"
        class="absolute top-1/2 -translate-y-1/2 right-0 size-13"
        :src="getAssetsImages(`${isVoted ? '已投票' : '未投票'}.png`)"
        mode="aspectFill"
      />

      <view class="flex-1 flex flex-col justify-between">
        <view class="font-500 text-sm line-clamp-2">
          {{ title }}
        </view>
        <view class="flex flex-col text-xs text-[#666] leading-4.5">
          <text>小区名称：{{ communityName }}</text>
          <text>开始时间：{{ startTime }}</text>
          <text>截止时间：{{ endTime }}</text>
        </view>
      </view>
    </view>
    <view class="py-3 flex items-center justify-between">
      <view class="flex items-center gap-x-1.5 text-xs">
        <text class="text-[#999]">每户一票</text>
        <text class="w-[1px] h-4 bg-[#D8D8D8]"></text>
        <text class="text-[#FF922E]">{{ voteCount }}人已参与</text>
      </view>
      <!-- <view class="flex items-center gap-x-1.5">
        <image class="size-5" mode="aspectFill" src="@/static/icons/arrange.png" />
        <text class="text-xs text-[#999]">安排</text>
      </view> -->
    </view>
  </view>
</template>

<style scoped></style>
