<script lang="ts" setup>
import { getFileIconFromUrl, getFileExtension, downloadFile, previewPDF } from '@/utils'
import type { FileCardProps } from './types.ts'

const props = withDefaults(defineProps<FileCardProps>(), {
  preView: true,
  bgColor: '#F4F8FB',
})

const handleClick = () => {
  if (!props.preView) return

  // 图片文件
  const imageExits = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  // office 文件
  const officeExits = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']

  const extension = getFileExtension(props.url)
  const lowerExtension = extension.toLowerCase()

  // 图片文件直接预览
  if (imageExits.includes(lowerExtension)) {
    return uni.previewImage({
      urls: [props.url],
      showmenu: true,
    })
  }

  // #ifndef H5
  // office 文件预览
  if (officeExits.includes(lowerExtension)) {
    uni.showLoading({
      title: '正在下载...',
      mask: true,
    })
    uni.downloadFile({
      url: props.url,
      success: (res) => {
        uni.hideLoading()
        uni.openDocument({
          filePath: res.tempFilePath,
          showmenu: true,
        })
      },
      fail: () => {
        uni.showToast({
          title: '下载失败',
          icon: 'error',
        })
        uni.hideLoading()
      },
    })
  }
  // #endif

  // #ifdef H5
  let previewUrl
  if (lowerExtension === 'pdf') {
    previewPDF(props.url, props.name)
  } else if (officeExits.includes(lowerExtension)) {
    const encodedUrl = encodeURIComponent(props.url)
    previewUrl = `https://www.pfile.com.cn/api/profile/onlinePreview?url=${encodedUrl}&title=${props.name}`
    window.open(previewUrl, '_blank')
  } else {
    downloadFile(props.url)
  }
  // #endif
}
</script>

// #ifdef MP-WEIXIN
<script lang="ts">
export default {
  options: {
    // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现(不会出现shadow节点下再去创建元素)
    virtualHost: true,
  },
}
</script>
// #endif

<template>
  <view
    :style="{ backgroundColor: bgColor }"
    class="flex items-center gap-x-2 overflow-hidden rounded-2 px-2 py-3"
    @tap="handleClick"
  >
    <image :src="getFileIconFromUrl(url)" class="h-8 w-8 shrink-0" mode="aspectFill" />
    <view class="line-clamp-2 text-[#666]">
      {{ name }}
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
