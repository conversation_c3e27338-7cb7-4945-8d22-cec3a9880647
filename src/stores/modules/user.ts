import {
  loginQuestionApi,
  loginTicket<PERSON>ode<PERSON>pi,
  loginVerifyCode<PERSON>pi,
  login<PERSON>orker<PERSON><PERSON>,
  touristLogin<PERSON><PERSON>,
} from '@/api/user/login.ts'
import type { LoginQuestionParams, LoginVerifyCodeParams, LoginWorkerParams, UserInfo } from '@/api/user/types.ts'
import { aesEncrypt } from '@/utils'

export const useUserStore = defineStore(
  'user',
  () => {
    // token
    const token = ref('')
    // 用户信息
    const userInfo = ref<UserInfo>()

    // 票权登录
    const loginTicketCodeAction = async (code: string, mobile?: string) => {
      const { token: ticketToken, user } = await loginTicket<PERSON>ode<PERSON>pi(code, mobile)
      token.value = ticketToken
      userInfo.value = user
    }
    // 验证码登录
    const loginVerifyCodeAction = async (params: LoginVerifyCodeParams) => {
      const { token: verifyToken, user } = await loginVerify<PERSON>odeApi({ ...params, mobile: aesEncrypt(params.mobile) })
      token.value = verifyToken
      userInfo.value = user
    }
    // 工作人员登录
    const loginWorkerAction = async (params: LoginWorkerParams) => {
      const { token: workerToken, user } = await loginWorkerApi({ ...params, mobile: aesEncrypt(params.mobile) })
      token.value = workerToken
      userInfo.value = user
    }
    // 问卷调查登录
    const loginQuestionAction = async (params: LoginQuestionParams) => {
      const cryptParams = {
        ...params,
        mobile: aesEncrypt(params.mobile),
        name: params.name ? aesEncrypt(params.name) : '',
        id_card: params.id_card ? aesEncrypt(params.id_card) : '',
      }
      const { token: questionToken, user } = await loginQuestionApi(cryptParams)
      token.value = questionToken
      userInfo.value = user
    }
    // 游客登录
    const touristLoginAction = async (params: any) => {
      const { token: touristToken, user } = await touristLoginApi(params)
      token.value = touristToken
      userInfo.value = user
    }
    // 获取用户信息
    const fetchUserInfoAction = () => {}
    // 退出登录
    const logoutAction = () => {
      token.value = ''
      userInfo.value = undefined
    }

    return {
      token,
      userInfo,
      fetchUserInfoAction,
      loginTicketCodeAction,
      loginVerifyCodeAction,
      loginWorkerAction,
      loginQuestionAction,
      logoutAction,
      touristLoginAction,
    }
  },
  { persist: true },
)
