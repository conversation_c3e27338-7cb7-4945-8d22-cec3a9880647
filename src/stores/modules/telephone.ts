import type { CallVoteStatusResult, CommunityListItem, MobileListItem } from '@/api/telephone/types'
import { getCallStatusByTicketApi, getCallVoteRecordsApi, getCommunityListApi } from '@/api/telephone'
import { groupBy } from 'lodash-es'

export const useTelephoneStore = defineStore(
  'telephone',
  () => {
    const mobileList = ref<MobileListItem[]>([])
    // 投票里面的通话记录
    const voteCallRecords = ref<any[]>([])
    // 小区列表
    const communityList = ref<CommunityListItem[]>([])
    // 投票状态
    const voteCallStatus = ref<CallVoteStatusResult>()

    // 获取投票里面的通话记录
    const fetchVoteCallRecordsAction = async (pqbm: string) => {
      const res = await getCallVoteRecordsApi(pqbm)
      const result = res.map((item: any) => ({ ...item, bd_time: item.bd_time.split(' ')[0] }))
      const groupResult = groupBy(result, 'bd_time')

      const newResult = []

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      for (const [_key, values] of Object.entries(groupResult)) {
        values[0].showTime = true
        newResult.push(...values)
      }
      voteCallRecords.value = newResult
    }
    // 获取小区列表
    const fetchCommunityListAction = async () => {
      communityList.value = await getCommunityListApi()
    }
    // 获取投票选项值
    const fetchCallStatusByTicketAction = async (pqbm: string) => {
      voteCallStatus.value = await getCallStatusByTicketApi(pqbm)
    }

    return {
      communityList,
      mobileList,
      voteCallRecords,
      voteCallStatus,
      fetchVoteCallRecordsAction,
      fetchCommunityListAction,
      fetchCallStatusByTicketAction,
    }
  },
  {
    persist: true,
  },
)
