import { getVoteDetailApi, getVoteResultApi, submitVoteApi } from '@/api/vote'
import type { VoteSubmitParams } from '@/api/vote/types.ts'
import type { VoteLisItem } from '@/api/ticket/types.ts'
import { getTicketListApi } from '@/api/ticket'
import { ScanCodeTypeEnum } from '@/api/user/types'
import type { TouristVoteParams } from '@/api/tourist/types'

export const useVoteStore = defineStore(
  'vote',
  () => {
    // 领取票权列表
    const ticketList = ref<any[]>()
    // 当前选择的投票id
    const currentVoteId = ref<string | number>('')
    // 投票列表
    const voteList = ref<VoteLisItem[]>([])
    // 投票详情
    const voteDetail = ref<any>({})
    // 投票参数
    const voteParams = ref<VoteSubmitParams>({
      pqbm: '',
      topic_id: '',
      item: [],
      qz_image: '',
      way: ScanCodeTypeEnum.链接,
    })
    // 游客投票参数
    const touristVoteParams = ref<TouristVoteParams>({
      topic_id: '',
      item: [],
      qz_image: '',
      way: ScanCodeTypeEnum.链接,
    })
    // 投票结果
    const voteResult = ref<VoteSubmitParams | null>(null)

    // 当前投票选项
    const currentVoteItem = computed(() => voteList.value.find((item) => item.id === currentVoteId.value))

    // 获取领取票权列表
    const fetchTicketListAction = async () => {
      ticketList.value = await getTicketListApi()
    }
    // 获取投票详情
    const fetchVoteDetailAction = async (uuid: string) => {
      voteDetail.value = await getVoteDetailApi(uuid, voteParams.value.way)
    }
    // 提交投票
    const submitVoteAction = async () => {
      voteParams.value.topic_id = currentVoteItem.value!.id
      voteParams.value.pqbm = currentVoteItem.value!.pqbm
      await submitVoteApi(voteParams.value)
      resetVoteParamsAction()
    }
    // 重置投票参数
    const resetVoteParamsAction = () => {
      voteParams.value = {
        pqbm: '',
        topic_id: '',
        item: [],
        qz_image: '',
        way: ScanCodeTypeEnum.链接,
      }
    }
    // 重置游客投票参数
    const resetTouristVoteParamsAction = () => {
      touristVoteParams.value = {
        topic_id: '',
        item: [],
        qz_image: '',
        way: ScanCodeTypeEnum.链接,
      }
    }
    // 获取投票结果
    const fetchVoteResultAction = async (code: string) => {
      voteResult.value = await getVoteResultApi(code)
    }

    return {
      currentVoteId,
      ticketList,
      voteList,
      currentVoteItem,
      voteDetail,
      voteParams,
      touristVoteParams,
      voteResult,
      fetchTicketListAction,
      fetchVoteDetailAction,
      submitVoteAction,
      resetVoteParamsAction,
      fetchVoteResultAction,
      resetTouristVoteParamsAction,
    }
  },
  {
    persist: true,
  },
)
