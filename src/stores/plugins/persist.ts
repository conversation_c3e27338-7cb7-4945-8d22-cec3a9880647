import type { Pinia } from 'pinia'
import type { PluginOptions } from 'pinia-plugin-persistedstate'
import { createPersistedState } from 'pinia-plugin-persistedstate'

/**
 * 注册 Pinia 持久化插件
 * @param pinia Pinia 实例
 */
export function registerPiniaPersistPlugin(pinia: Pinia) {
  pinia.use(createPersistedState(createPersistedStateOptions()))
}

/**
 * 创建持久化状态选项
 * @param keyPrefix prefix 储存键前缀
 * @returns
 */
export function createPersistedStateOptions(keyPrefix = '__store__'): PluginOptions {
  return {
    key: (id) => `${keyPrefix}__${id}`,
    storage: {
      setItem(key, value) {
        uni.setStorageSync(key, value)
      },
      getItem(key) {
        return uni.getStorageSync(key)
      },
    },
  }
}
