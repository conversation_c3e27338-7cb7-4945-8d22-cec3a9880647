import { request } from '@/utils/request'
import { ScanCodeTypeEnum } from '../user/types'
import type { PageParams, PageResult } from '#/request'
import type { SubmitHouseProofParams, TouristVoteParams } from './types'
import type { VotePublicityParams } from '../vote/types'

export enum Apis {
  HOUSE_LIST = '/api/Tourist/getRoomList', // 房屋列表
  TOURIST_TOPIC_LIST = '/api/Tourist/getMyTopicList', // 游客投票列表
  TOURIST_TOPIC_DETAIL = '/api/Tourist/getTopicItems', // 游客投票详情
  TOURIST_TOPIC_RESULT = '/api/Tourist/getVoteDetail', // 游客投票结果
  NEW_TOURIST_TOPIC_RESULT = '/api/Tourist/getTouristVoteDetail', // 分享进来的投票结果
  SUBMIT_VOTE = '/api/Tourist/voteCommit', // 提交投票
  GET_AUTH_CODE = '/api/tourist/getAuthCode', // 生成授权二维码
  CONFIRM_AUTH = '/api/tourist/compareTourist', // 金标用户对比
  SUBMIT_HOUSE_PROOF = '/api/Tourist/uploadHouseImg', // 提交房产证明
  ALL_RECORD = '/api/Tourist/getAllRecord', // 结果公示
}

/**
 * @description 房屋列表
 * @returns {Promise<any>}
 */
export const getRoomListApi = (params: PageParams) => request.post<PageResult>(Apis.HOUSE_LIST, params)

/**
 * @description 游客投票列表
 * @returns {Promise<any>}
 */
export const getTouristTopicListApi = (params: PageParams) => request.post<PageResult>(Apis.TOURIST_TOPIC_LIST, params)

/**
 * @description 投票详情
 * @param topic_id 投票id
 * @returns {Promise<any>}
 */
export const getTouristTopicDetailApi = (topic_id: string | number, way = ScanCodeTypeEnum.链接) => {
  return request.post<any>(Apis.TOURIST_TOPIC_DETAIL, { topic_id, way })
}

/**
 * @description 投票结果
 * @param topic_id 投票id
 * @returns {Promise<any>}
 */
export const getTouristTopicResultApi = (topic_id: string | number) => {
  return request.post<any>(Apis.TOURIST_TOPIC_RESULT, { topic_id })
}
/**
 * @description 投票结果 新的
 * @param topic_id 投票id
 * @returns {Promise<any>}
 */
export const getNewTouristTopicResultApi = (id: string | number) => {
  return request.post<any>(Apis.NEW_TOURIST_TOPIC_RESULT, { id })
}

/**
 * @description 提交投票
 * @param data 投票数据
 * @returns {Promise<any>}
 */
export const submitTouristVoteApi = (data: TouristVoteParams) => request.post(Apis.SUBMIT_VOTE, data)

/**
 * @description 生成授权二维码
 * @param topic_id 议题id
 * @returns {Promise<any>}
 */
export const getAuthCodeApi = (topic_id: string | number) => request.post(Apis.GET_AUTH_CODE, { topic_id })

/**
 * @description 金标用户对比
 * @param id 小程序码携带的id
 * @returns {Promise<any>}
 */
export const confirmAuthApi = (id: string | number) => request.post(Apis.CONFIRM_AUTH, { id })

/**
 * @description 提交房产证明
 * @param data 房产证明数据
 * @returns {Promise<any>}
 */
export const submitHouseProofApi = (data: any) => request.post(Apis.SUBMIT_HOUSE_PROOF, data)

/**
 * @description 结果公示
 * @param data 结果公示数据
 * @returns {Promise<any>}
 */
export const getAllRecordApi = (data: VotePublicityParams) => request.post(Apis.ALL_RECORD, data)
