import { request } from '@/utils/request'
import type { UploadAuthInfoParams, VotePublicityParams, VoteSubmitParams } from '@/api/vote/types.ts'
import type { ScanCodeTypeEnum } from '../user/types'

export enum Apis {
  VOTE_DETAIL = '/api/ticket/getTopicItems', // 投票详情
  SUBMIT_VOTE = '/api/ticket/voteCommit', // 提交投票
  VOTE_RESULT = '/api/ticket/getVoteDetail', // 投票结果
  VOTE_PUBLICITY = '/api/ticket/getAllRecord', // 投票公示
  UPLOAD_AUTH_INFO = '/api/ticket/uploadHouseImg', // 上传房产信息
  RESULT_TABLE_HEADER = '/api/ticket/getHeader', // 结果公示表头
}

/**
 * @description 投票详情 (投票准备、投票详情、投票结果页面都需要用到)
 * @param uuid 投票uuid
 */
export const getVoteDetailApi = (uuid: string, way: ScanCodeTypeEnum) => request.get(Apis.VOTE_DETAIL, { uuid, way })

/**
 * @description 提交投票
 * @param data 投票参数
 * @returns {Promise<any>}
 */
export const submitVoteApi = (data: VoteSubmitParams) => request.post(Apis.SUBMIT_VOTE, data)

/**
 * @description 投票结果
 * @param {string} pqbm 投票编码
 * @returns {Promise<any>}
 */
export const getVoteResultApi = (pqbm: string) => request.get(Apis.VOTE_RESULT, { pqbm })

/**
 * @description 查看投票公示
 * @param data
 * @returns {Promise<any>}
 */
export const getVotePublicityApi = (data: VotePublicityParams) => request.post(Apis.VOTE_PUBLICITY, data)

/**
 * @description 上传授权信息
 * @param data
 * @returns {Promise<any>}
 */
export const uploadAuthInfoApi = (data: UploadAuthInfoParams) => request.post(Apis.UPLOAD_AUTH_INFO, data)

/**
 * @description 结果公示表头
 * @param {string} uuid 投票uuid
 * @returns {Promise<any>}
 */
export const getResultTableHeaderApi = (uuid: string) => request.get(Apis.RESULT_TABLE_HEADER, { uuid })
