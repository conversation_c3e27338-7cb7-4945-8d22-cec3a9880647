import type { ScanCodeTypeEnum } from '../user/types'

/**
 * @description 投票选项类型
 */
export enum VoteOptionsTypeEnum {
  /**
   * @description 单选
   */
  SINGLE = 1,
  /**
   * @description 多选
   */
  MULTIPLE,
}

/**
 * @description 投票事项
 */
export interface VoteMatterItem {
  /**
   * @description 事项id
   */
  id: number
  /**
   * @description 选项类型
   */
  type: VoteOptionsTypeEnum
  /**
   * @description 选项id
   */
  option_id: string[]
}

/**
 * @description 投票入参
 */
export interface VoteSubmitParams {
  /**
   * @description 票权编码
   */
  pqbm: string
  /**
   * @description 主题id（投票id）
   */
  topic_id: number | string
  /**
   * @description 事项
   */
  item: Array<VoteMatterItem | undefined>
  /**
   * @description 签名图片
   */
  qz_image: string
  way: ScanCodeTypeEnum
}

/**
 * @description 投票公示入参
 */
export interface VotePublicityParams {
  /**
   * @description uuid
   */
  uuid: string
  /**
   * @description 票权编码
   */
  pqbm?: string
}

export interface UploadAuthInfoParams {
  /**
   * @description 票权编码
   */
  pq_id: string | number
  /**
   * @description 房产信息图片
   */
  img: string[]
}
