export enum LoginChannelEnum {
  /**
   * @description 其他小程序跳转过来
   */
  MINI_PROGRAM = 1,
  /**
   * @description 姓名+手机号登录
   */
  NAME_MOBILE,
}

export enum ScanCodeTypeEnum {
  通用码 = 1,
  链接 = 2,
  一户一码 = 4,
}

export enum IsPassCheckEnum {
  金标 = 1,
  灰标,
  游客,
}

/**
 * @description 用户身份
 */
export enum UserTypeEnum {
  /**
   * @description 话务人员
   */
  AGENT = 1,
  /**
   * @description 业主
   */
  OWNER = 100,
}

/**
 * @description 验证码登录参数
 */
export interface LoginVerifyCodeParams {
  /**
   * @description 手机号
   */
  mobile: string
  /**
   * @description 短信验证码
   */
  code: string
  /**
   * @description 登录渠道
   */
  channel?: LoginChannelEnum
  /**
   * @description 姓名 (channel为 NAME_MOBILE=2 时必填)
   */
  name?: string
}

/**
 * @description 票权登录结果
 */
export interface LoginTicketCodeResult {
  /**
   * @description token
   */
  token: string
  /**
   * @description 类型
   */
  type: string
  /**
   * @description 是否投票
   */
  is_vote: number
  /**
   * @description 用户信息
   */
  user: UserInfo
}

/**
 * @description 验证码登录结果
 */
export interface LoginVerifyCodeResult {
  /**
   * @description token
   */
  token: string
  /**
   * @description 用户信息
   */
  user: UserInfo
}

/**
 * @description 用户信息
 */
export interface UserInfo {
  /**
   * @description 用户名
   */
  name: string
  /**
   * @description 手机号
   */
  mobile: string
  /**
   * @description 身份
   */
  type?: UserTypeEnum
  /**
   * @description 住建系统对比成功 1==>成功，2==>没有对比成功 3==>游客
   */
  is_pass_check: IsPassCheckEnum
  /**
   * @description 游客ID
   */
  tourist_id?: string | number
}

/**
 * @description 工作人员登录参数
 */
export interface LoginWorkerParams {
  /**
   * @description 手机号
   */
  mobile: string
  /**
   * @description 密码
   */
  password: string
}

export interface LoginWorkerResult {
  /**
   * @description token
   */
  token: string
  /**
   * @description 用户信息
   */
  user: UserInfo
}

/**
 * @description 问卷调查登录参数
 */
export interface LoginQuestionParams {
  /**
   * @description 手机号
   */
  mobile: string
  /**
   * @description 姓名
   */
  name?: string
  /**
   * @description 出生年份
   */
  birth_year?: string
  /**
   * @description 身份证号
   */
  id_card?: string
  /**
   * @description 性别
   */
  sex?: string
  /**
   * @description 问卷ID
   */
  question_id: string
}

/**
 * @description 游客登录结果
 */
export interface TouristLoginResult {
  token: string
  user: UserInfo
}

export interface ScanCodeLoginParams {
  topic_id: string | number
  /**
   * @description 小程序openid
   */
  openid?: string
  mobile: string
  type: ScanCodeTypeEnum
}
