import { request } from '@/utils/request'
import type {
  LoginQuestionParams,
  LoginTicketCodeResult,
  LoginVerifyCodeParams,
  LoginVerifyCodeResult,
  LoginWorkerParams,
  LoginWorkerResult,
  ScanCodeLoginParams,
  TouristLoginResult,
} from './types'

export enum Apis {
  LOGIN_VERIFY_CODE = '/api/login/loginByVerificationCode', // 验证码登录
  LOGIN_TICKET_CODE = '/api/login/loginByTicketCode', // 票权登录
  SEND_VERIFY_CODE = '/api/login/getLoginCode', // 发送验证码
  VIEW_CODE = '/api/login/viewCode', // 访问二维码
  LOGIN_WORKER = '/api/login/loginByWorkerName', // 工作人员登录
  LOGIN_QUESTION = '/api/login/loginAfterWx', // 问卷调查登录
  LOGIN_SCAN_CODE = '/api/login/scanCode', // 扫码登录
  GET_TOPIC_VERIFY_TYPE = '/api/login/getTopicVerifyType', // 获取议题验证类型
  TOURIST_LOGIN = '/api/login/touristLogin', // 游客登录
}

/**
 * @description 验证码登录
 * @param {LoginVerifyCodeParams} params
 * @returns {Promise<any>}
 */
export const loginVerifyCodeApi = (params: LoginVerifyCodeParams) =>
  request.post<LoginVerifyCodeResult>(Apis.LOGIN_VERIFY_CODE, params)

/**
 * @description 票权登录
 * @param {string} pqbm 票权编码
 * @param {string} mobile 手机号码
 * @returns
 */
export const loginTicketCodeApi = (pqbm: string, mobile?: string) => {
  return request.post<LoginTicketCodeResult>(Apis.LOGIN_TICKET_CODE, { pqbm, mobile })
}

/**
 * @description 发送验证码
 * @param {string} mobile 手机号
 * @returns
 */
export const sendVerifyCodeApi = (mobile: string) => request.post(Apis.SEND_VERIFY_CODE, { mobile })

/**
 * @description 访问二维码
 * @param {number} topic_id 投票议题ID
 * @returns {Promise<any>}
 */
export const getViewCodeApi = (topic_id: number) => request.get(Apis.VIEW_CODE, { topic_id })

/**
 * @description 工作人员登录
 * @param {LoginWorkerParams} params
 * @returns {Promise<any>}
 */
export const loginWorkerApi = (params: LoginWorkerParams) => request.post<LoginWorkerResult>(Apis.LOGIN_WORKER, params)

/**
 * @description 问卷调查登录
 * @param {LoginQuestionParams} params
 * @returns {Promise<any>}
 */
export const loginQuestionApi = (params: LoginQuestionParams) => request.post(Apis.LOGIN_QUESTION, params)

/**
 * @description 扫码登录
 * @param
 * @returns
 */
export const scanCodeLoginApi = (params: ScanCodeLoginParams) => request.post(Apis.LOGIN_SCAN_CODE, params)

/**
 * @description 获取议题验证类型
 * @param {string | number} topic_id_or_pq 议题ID或票权编码
 * @returns {Promise<any>}
 */
export const getTopicVerifyTypeApi = (topic_id_or_pq: string | number) => {
  return request.get(Apis.GET_TOPIC_VERIFY_TYPE, { topic_id_or_pq })
}

/**
 * @description 游客登录
 * @returns {Promise<any>}
 */
export const touristLoginApi = (params: any) => request.post<TouristLoginResult>(Apis.TOURIST_LOGIN, params)
