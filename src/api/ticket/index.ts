import { request } from '@/utils/request'
import type { VoteListResult, VoteMyListParams } from './types.ts'

export enum Apis {
  GET_TICKET_LIST = '/api/ticket/getTicketList', // 领取票权列表
  RECEIVE_TICKET = '/api/ticket/getTickets', // 领取票权
  VOTE_LIST = '/api/ticket/getMyTopicList', // 投票列表
}

/**
 * @description 领取票权列表
 * @returns {Promise<any>}
 */
export const getTicketListApi = () => request.get(Apis.GET_TICKET_LIST)

/**
 * @description 领取票权
 * @param {string[]} pqbm 票权编码
 * @returns {Promise<any>}
 */
export const receiveTicketApi = (pqbm: string[]) => request.post(Apis.RECEIVE_TICKET, { pqbm })

/**
 * @description 我的投票列表
 * @returns {Promise<any>}
 */
export const getMyVoteListApi = (params: VoteMyListParams) => request.get<VoteListResult>(Apis.VOTE_LIST, params)
