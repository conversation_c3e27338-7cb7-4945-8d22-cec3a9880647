import type { PageParams, PageResult } from '#/request'

/**
 * @description 投票状态
 */
export enum VoteStatusEnum {
  /**
   * @description 未投票
   */
  UN_VOTE = 2,
  /**
   * @description 已投票
   */
  VOTED,
}

/**
 * @description 投票进度
 */
export enum VoteProgressEnum {
  /**
   * @description 未开始
   */
  UN_START = 1,
  /**
   * @description 进行中
   */
  IN_PROGRESS,
  /**
   * @description 已结束
   */
  FINISHED,
}

/**
 * @description 我的投票列表参数
 */
export interface VoteMyListParams extends PageParams {
  /**
   * @description 状态
   */
  status?: VoteStatusEnum
  /**
   * @description 搜索关键字
   */
  keyword?: string
}

/**
 * @description 投票列表项
 */
export interface VoteLisItem {
  /**
   * @description 投票ID
   */
  id: number
  /**
   * @description 投票UUID（详情使用）
   */
  uuid: string
  /**
   * @description 投票标题
   */
  title: string
  /**
   * @description 票权编码
   */
  pqbm: string
  /**
   * @description 小区名称
   */
  community_name: string
  /**
   * @description 投票开始时间
   */
  s_time_str: string
  /**
   * @description 投票结束时间
   */
  e_time_str: string
  /**
   * @description 投票状态
   */
  status: VoteStatusEnum
  /**
   * @description 参与人数
   */
  join_num: number
  /**
   * @description 投票进度
   */
  is_start: VoteProgressEnum
  /**
   * @description 楼栋名称
   */
  floor_name: string
  /**
   * @description 单元名称
   */
  unit_name: string
  /**
   * @description 房号
   */
  room_name: string
  /**
   * @description 面积
   */
  area: string
  /**
   * @description 票权id
   */
  pq_id: string | number
}

/**
 * @description 投票列表返回值
 */
export type VoteListResult = PageResult<VoteLisItem>
