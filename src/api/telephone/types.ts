import type { FileParams, PageParams, PageResult } from '#/request'
import type { VoteMatterItem } from '@/api/vote/types.ts'

/**
 * @description 通讯状态
 */
export enum CallStatusEnum {
  /**
   * @description 未拨打
   */
  UN_CALL = 1,
  /**
   * @description 已拨打
   */
  CALL,
}

/**
 * @description 投票状态(电话投票状态)
 */
export enum MobileVoteStatusEnum {
  /**
   * @description 已投票
   */
  VOTED = 1,
  /**
   * @description 弃权
   */
  ABSTAINED,
  /**
   * @description 未表态
   */
  NOT_VOTED,
}

/**
 * @description 接通状态
 */
export enum JtStatusEnum {
  /**
   * @description 接通
   */
  CONNECTED = 1,
  /**
   * @description 无人接听
   */
  NO_ANSWER,
  /**
   * @description 空号
   */
  EMPTY_NUMBER,
  /**
   * @description 挂断
   */
  HANG_UP,
}

/**
 * @description 获取通讯列表参数
 */
export interface MobileListParams extends PageParams {
  /**
   * @description 搜索关键字
   */
  keyword?: string
  /**
   * @description 是否拨打
   */
  is_call?: CallStatusEnum
}

/**
 * @description 通讯列表项
 */
export interface MobileListItem {
  /**
   * @description id
   */
  id: number
  /**
   * @description 地址
   */
  address: string
  /**
   * @description 用户信息
   */
  user: Array<{
    /**
     * @description 姓名
     */
    name: string
    /**
     * @description 手机号
     */
    mobile: string
  }>
  /**
   * @description 投票状态
   */
  dhtp_status: MobileVoteStatusEnum
  /**
   * @description 接通状态
   */
  jt_status: JtStatusEnum
  /**
   * @description 议题uuid
   */
  topic_uuid: string
  /**
   * @description 议题id
   */
  topic_id: number
  /**
   * @description 票权编码
   */
  pqbm: string
  /**
   * @description 面积
   */
  area: number
}

export type MobileListResult = PageResult<MobileListItem>

export interface MobileVoteSubmitParams {
  /**
   * @description 议题id
   */
  topic_id: number | string
  /**
   * @description 票权编码
   */
  pqbm: string
  /**
   * @description 接通状态
   */
  jt_status: JtStatusEnum | ''
  /**
   * @description 姓名
   */
  name: string
  /**
   * @description 手机号
   */
  mobile: string
  /**
   * @description 投票状态
   */
  dhtp_status: MobileVoteStatusEnum | ''
  /**
   * @description 事项选择
   */
  item: VoteMatterItem[]
  /**
   * @description 录音文件
   */
  voice: FileParams[]
}

/**
 * @description 小区列表项
 */
export interface CommunityListItem {
  /**
   * @description 小区id
   */
  id: number
  /**
   * @description 小区名称
   */
  name: string
}

/**
 * @description 投票状态结果(电话投票状态)
 */
export interface CallVoteStatusResult {
  /**
   * @description 投票状态
   */
  dhtp_status: MobileVoteStatusEnum
  /**
   * @description 接通状态
   */
  jt_status: JtStatusEnum
  /**
   * @description 用户手机号
   */
  user_mobile: string
  /**
   * @description 用户姓名
   */
  user_name: string
}
