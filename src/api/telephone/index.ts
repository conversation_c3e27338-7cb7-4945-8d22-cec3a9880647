import { request } from '@/utils/request'
import type {
  CallVoteStatusResult,
  CommunityListItem,
  MobileListParams,
  MobileListResult,
  MobileVoteSubmitParams,
} from './types.ts'
import type { PageParams, PageResult } from '#/request'

export enum Apis {
  MOBILE_LIST = '/api/worker/getMobileList', // 通讯列表
  MOBILE_VOTE_SUBMIT = '/api/worker/commitCallResult', // 电话投票
  CALL_RECORDS = '/api/worker/getCallList', // 拨打记录
  CALL_RECORDS_VOTE = '/api/worker/getCallVoiceAndLog', // 票权里面的通话记录
  COMMUNITY_LIST = '/api/worker/getCommunityList', // 小区列表
  SWITCH_COMMUNITY = '/api/worker/changeCommunity', // 切换小区
  CALL_STATUS_BY_TICKET = '/api/worker/getCallStatusByTicket', // 投票状态
}

/**
 * @description: 获取通讯列表
 * @param {MobileListParams} params
 * @return {*}
 */
export const getMobileListApi = (params: MobileListParams) => request.post<MobileListResult>(Apis.MOBILE_LIST, params)

/**
 * @description: 提交电话投票
 * @param {MobileVoteSubmitParams} params
 * @returns {Promise<any>}
 */
export const submitMobileVoteApi = (params: MobileVoteSubmitParams) => request.post(Apis.MOBILE_VOTE_SUBMIT, params)

/**
 * @description: 获取拨打记录
 * @param {PageParams} params
 * @returns {Promise<any>}
 */
export const getCallRecordsApi = (params: PageParams) => request.post<PageResult>(Apis.CALL_RECORDS, params)

/**
 * @description: 获取票权里面的通话记录
 * @param {string} pqbm 票权编码
 * @returns {Promise<PageResult>}
 */
export const getCallVoteRecordsApi = (pqbm: string) => request.post<any[]>(Apis.CALL_RECORDS_VOTE, { pqbm })

/**
 * @description: 获取小区列表
 * @returns
 */
export const getCommunityListApi = () => request.post<CommunityListItem[]>(Apis.COMMUNITY_LIST)

/**
 * @description 切换小区
 * @param community_id 小区id
 * @returns
 */
export const switchCommunityApi = (community_id: number) => request.post(Apis.SWITCH_COMMUNITY, { community_id })

/**
 * @description 获取投票状态
 * @param pqbm 票权编码
 * @returns
 */
export const getCallStatusByTicketApi = (pqbm: string) => {
  return request.post<CallVoteStatusResult>(Apis.CALL_STATUS_BY_TICKET, { pqbm })
}
