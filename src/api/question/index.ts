import { request } from '@/utils/request'

export enum Apis {
  GET_QUESTIONNAIRE = '/api/questionnaire/getQuestionnaire', // 问卷题目及记录
  GET_QUESTION_EXPLAIN = '/api/questionnaire/questionnaireDetail', // 问卷详情
  SUBMIT_QUESTIONNAIRE = '/api/questionnaire/commitQuestionnaire', // 提交问卷
}

/**
 * @description 问卷题目及记录
 * @returns {Promise<any>}
 */
export const getQuestionnaireApi = () => request.get(Apis.GET_QUESTIONNAIRE)

/**
 * @description 问卷详情（问卷说明）
 * @returns {Promise<any>}
 */
export const getQuestionExplainApi = () => request.get(Apis.GET_QUESTION_EXPLAIN)

/**
 * @description 提交问卷
 * @returns {Promise<any>}
 */
export const submitQuestionApi = (data: any) => request.post(Apis.SUBMIT_QUESTIONNAIRE, data)
