/// <reference types="vite/client" />

import type { Uni as _Uni } from '@dcloudio/types'
import route from '@/utils/route'

declare module '*.vue' {
  import { DefineComponent } from 'vue'

  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare global {
  interface Window {
    APP_VERSION: string
  }
  interface Uni extends _Uni {
    $zp: ZPagingGlobal
    $tui: any
    $t: {
      route: typeof route
    }
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $t: {
      route: typeof route
    }
  }
}

interface ImportMetaEnv {
  /** 网站标题，应用名称 */
  readonly VITE_APP_TITLE: string
  /** 服务端口号 */
  readonly VITE_SERVER_PORT: string
  /** 后台接口地址 */
  readonly VITE_SERVER_BASEURL: string
  /** H5是否需要代理 */
  readonly VITE_APP_PROXY: 'true' | 'false'
  /** H5是否需要代理，需要的话有个前缀 */
  readonly VITE_APP_PROXY_PREFIX: string
  /** 是否清除 console 和 debugger  */
  readonly VITE_DELETE_CONSOLE: 'true' | 'false'
  /** 是否开启 sourcemap */
  readonly VITE_SHOW_SOURCEMAP: 'true' | 'false'
  /** h5部署网站的base */
  readonly VITE_APP_PUBLIC_BASE: string
  /** 加密密钥 */
  readonly VITE_CRYPTO_KEY: string
  /** 加密偏移量 */
  readonly VITE_CRYPTO_IV: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
