import { colorToRgba } from '@/utils'
import { NAV_BAR_ID, PAGE_CONTAINER_ID } from '@/config'

export const useNavBarColor = (bgColor: string) => {
  const { getBoundingClientRect } = useSelectorQuery()

  const navBarTop = ref<number>(0)
  const navBarChangeBaseLineHeight = ref<number>(0)
  const navBarBackgroundColor = ref(colorToRgba(bgColor, 0))

  onReady(async () => {
    await nextTick()
    initNavBarRectInfo()
  })
  onPageScroll(() => {
    updateNavBarRectInfo()
  })

  const initNavBarRectInfo = async () => {
    const navBarRectInfo = await getBoundingClientRect(`#${NAV_BAR_ID}`)
    const pageTipsRectInfo = await getBoundingClientRect(`#${PAGE_CONTAINER_ID}`)
    if (!navBarRectInfo || !pageTipsRectInfo) return
    navBarTop.value = navBarRectInfo.top!
    navBarChangeBaseLineHeight.value = pageTipsRectInfo.top! - navBarRectInfo.top!
  }
  const updateNavBarRectInfo = async () => {
    const res = await getBoundingClientRect(`#${PAGE_CONTAINER_ID}`)
    const top = res?.top || 0
    const differHeight = top - navBarTop.value

    let opacity = differHeight / navBarChangeBaseLineHeight.value
    if (opacity > 1) opacity = 1
    navBarBackgroundColor.value = opacity < 0 ? colorToRgba(bgColor, 1) : colorToRgba(bgColor, 1 - opacity)
  }

  return { navBarBackgroundColor, updateNavBarRectInfo }
}
