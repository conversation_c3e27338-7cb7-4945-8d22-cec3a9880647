<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
  // 只在生产环境检查版本
  if (process.env.NODE_ENV === 'production') {
    const version: string = window.APP_VERSION || ''
    const lastVersion: string | null = localStorage.getItem('app_version')

    if (lastVersion !== version) {
      localStorage.setItem('app_version', version)
      location.reload() // 强制刷新获取新资源
    }
  }
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>
<style></style>
