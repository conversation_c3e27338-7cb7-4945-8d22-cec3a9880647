import CryptoJS from 'crypto-js'

/**
 * @description 获取 aes 的 key 和 iv
 * @returns
 */
export const getAesKeyAndIv = () => {
  const key = import.meta.env.VITE_CRYPTO_KEY
  const iv = import.meta.env.VITE_CRYPTO_IV

  console.log('key', key)
  console.log('iv', iv)
  return {
    key: CryptoJS.enc.Utf8.parse(key),
    iv: CryptoJS.enc.Utf8.parse(iv),
  }
}

getAesKeyAndIv()

/**
 * @description aes 加密
 * @param value 需要加密的字符串
 * @returns 加密后的字符串
 */
export const aesEncrypt = (value: string) => {
  const { key, iv } = getAesKeyAndIv()

  const encrypted = CryptoJS.AES.encrypt(value, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })

  return base64Encrypt(CryptoJS.enc.Base64.stringify(encrypted.ciphertext))
}

/**
 * @description aes 解密
 * @param value 需要解密的字符串
 * @returns 解密后的字符串
 */
export const aesDecrypt = (value: string) => {
  const { key, iv } = getAesKeyAndIv()

  console.log('解密 value', value)

  const decrypted = CryptoJS.AES.decrypt(base64Decrypt(value), key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })

  return CryptoJS.enc.Utf8.stringify(decrypted).replace(/^"|"$/g, '')
}

/**
 * @description base64 解密
 * @param cipher 需要解密的字符串
 * @returns 解密后的字符串
 */
export const base64Decrypt = (cipher: string) => {
  return CryptoJS.enc.Base64.parse(cipher).toString(CryptoJS.enc.Utf8)
}

/**
 * @description base64 加密
 * @param str 需要加密的字符串
 * @returns 加密后的字符串
 */
export const base64Encrypt = (str: string) => {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str))
}
