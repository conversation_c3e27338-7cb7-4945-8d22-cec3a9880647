import type { UnCanceler, UnConfig, UnData } from '@uni-helper/uni-network'
import { un } from '@uni-helper/uni-network'
import qs from 'qs'

const pendingRequest = new Map<string, UnCanceler<UnData, UnData>>()

/**
 * @description 生成请求的唯一标识
 * @param config
 */
const generateReqKey = (config: UnConfig) => {
  const { method, url, params, data } = config
  return [method, url, qs.stringify(params), qs.stringify(data)].join('&')
}

/**
 * @description 添加当前请求到 pendingRequest 中
 * @param config
 */
export const addPendingRequest = (config: UnConfig) => {
  const requestKey = generateReqKey(config)
  config.cancelToken =
    config.cancelToken ||
    new un.CancelToken((cancel) => {
      if (!pendingRequest.has(requestKey)) {
        pendingRequest.set(requestKey, cancel)
      }
    })
}

/**
 * @description 如果请求在 pendingRequest 中存在，则取消请求
 * @param config
 */
export const removePendingRequest = (config: UnConfig) => {
  const requestKey = generateReqKey(config)
  if (pendingRequest.has(requestKey)) {
    const cancelToken = pendingRequest.get(requestKey)!
    cancelToken(requestKey)
    pendingRequest.delete(requestKey)
  }
}
