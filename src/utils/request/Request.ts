import type { HttpResult } from '#/request'
import type { UnConfig, UnData, UnError, UnInstance, UnParams } from '@uni-helper/uni-network'
import { un as uniNetwork } from '@uni-helper/uni-network'
import { useUserStore } from '@/stores'
import { addPendingRequest, removePendingRequest } from './cancel-request'
import { authLogin } from '../auth'

export class Request {
  private readonly unInstance: UnInstance

  constructor(readonly config: UnConfig) {
    this.unInstance = uniNetwork.create(config)
    this.setupInterceptors()
  }

  public get<T = any>(url: string, params?: UnParams, config?: UnConfig): Promise<T> {
    return this.request({ url, method: 'GET', params, ...config })
  }

  public post<T = any>(url: string, data?: UnData, config?: UnConfig): Promise<T> {
    return this.request({ url, method: 'POST', data, ...config })
  }

  public upload<T = any>(url: string, config?: UnConfig): Promise<T> {
    return this.request({ url, ...config, adapter: 'upload' })
  }

  private setupInterceptors() {
    this.unInstance.interceptors.request.use(
      (config) => {
        const userStore = useUserStore()
        console.log(config,'-config');
        
        config.headers = {
          ...config.headers,
          // 请求头，根据实际情况添加（token等等）
          Authorization: userStore.token,
        }

        if (config.adapter === 'request') {
          removePendingRequest(config) // 检查是否有重复请求，若存在则取消已发的请求
          addPendingRequest(config) // 把当前请求信息添加到pendingRequest对象中
        }
        return config
      },
      (err: UnError) => {
        return Promise.reject(err)
      },
    )

    this.unInstance.interceptors.response.use(
      (response) => {
        const adapter = response.config?.adapter
        if (adapter === 'request') {
          removePendingRequest(response.config!) // 从pendingRequest对象中移除请求
          return response.data
        } else if (adapter === 'upload') {
          return JSON.parse(response.data as string)
        }
      },
      (err: UnError) => {
        removePendingRequest(err.config || {})
        if (uniNetwork.isCancel(err)) {
          console.log(`已取消重复请求：${err.message}`)
        }
        return Promise.reject(err)
      },
    )
  }

  private request<T>(config: UnConfig): Promise<T> {
    return new Promise((resolve, reject) => {
      this.unInstance
        .request(config)
        .then((res) => {
          const { code, data, msg, status } = res as HttpResult
          if (code === 200 && status === 1) {
            resolve(data)
          } else if (code === 401) {
            authLogin()
          } else {
            uni.showToast({ title: msg, icon: 'error' })
            reject(msg)
          }
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}
