import { baseURL } from '@/config'
import { Request } from './Request'

const isProxy = JSON.parse(import.meta.env.VITE_APP_PROXY) && import.meta.env.MODE === 'development'

let httpUrl
// #ifdef H5
httpUrl = isProxy ? import.meta.env.VITE_APP_PROXY_PREFIX : baseURL
// #endif
// #ifndef H5
httpUrl = baseURL
// #endif

export const request = new Request({
  baseUrl: httpUrl,
  timeout: 10000,
  headers: {
    channel: 'tmj',
  },
})
