import { UserTypeEnum } from '@/api/user/types'
import { useUserStore } from '@/stores'
import { getCurrentPagePath } from '.'

export const authLogin = () => {
  const { userInfo } = useUserStore()
  const currentPage = getCurrentPages().at(-1)!
  uni.showModal({
    title: '提示',
    content: '登录失效，请重新登录',
    confirmColor: '#FA483B',
    success: ({ confirm }) => {
      if (!confirm) return

      let loginRoute: string
      if (!userInfo) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        if (currentPage.options.isWorker && JSON.parse(currentPage.options.isWorker)) {
          loginRoute = '/pages/login/worker'
        } else {
          loginRoute = '/pages/login/index'
        }
        uni.$t.route({
          type: 'reLaunch',
          url: loginRoute,
        })
      } else {
        loginRoute = userInfo?.type === UserTypeEnum.OWNER ? '/pages/login/index' : '/pages/login/worker'
        uni.$t.route({
          type: 'reLaunch',
          url: loginRoute,
          params: {
            redirect: encodeURIComponent(getCurrentPagePath()),
          },
        })
      }
    },
  })
}
