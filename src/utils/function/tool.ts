import * as pdfjsLib from 'pdfjs-dist'
import { isEmpty, isNumber } from './is'

// 使用 CDN worker 确保版本匹配
pdfjsLib.GlobalWorkerOptions.workerSrc = new URL('/src/static/js/pdf.worker.min.js', import.meta.url).href

/**
 * @description: 获取静态资源图片路径
 * @param {string} name 图片名称
 * @returns {string} 图片路径
 */
export const getAssetsImages = (name: string) => {
  return new URL(`/src/static/images/${name}`, import.meta.url).href
}

/**
 * @description 根据文件 URL 提取文件后缀
 * @param {String} url 文件的完整 URL
 * @returns {String} 文件后缀
 */
export function getFileExtension(url: string) {
  // 提取文件后缀
  const extensionMatch = url.match(/\.([a-zA-Z0-9]+)$/)
  const extension = extensionMatch ? extensionMatch[1] : ''
  // 返回结果
  return extension
}

/**
 * @description 根据文件 URL 获取文件图标
 * @param url
 * @returns {String|Array<String>}
 */
export function getFileIconFromUrl(url: string) {
  const extension = getFileExtension(url)
  const extResult = extension.toLowerCase()

  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extResult)) {
    return getAssetsImages('image')
  }

  // office 文件后缀和 文件 icon 映射
  const officeIconMap = new Map([
    ['word', ['doc', 'docx']],
    ['excel', ['xls', 'xlsx']],
    ['pdf', ['pdf']],
    ['ppt', ['ppt', 'pptx']],
  ])

  for (const [key, value] of officeIconMap.entries()) {
    if (value.includes(extResult)) {
      return getAssetsImages(`${key}.png`)
    }
  }
  // 其他文件
  return getAssetsImages('file.png')
}

/**
 * @description 下载文件，适配 H5 和小程序，支持自定义文件名，返回 Promise
 * @param url 文件下载地址
 * @param filename 文件名（可选）
 * @returns Promise<string> 下载成功返回文件名或本地路径
 */
export function downloadFile(url: string, filename?: string): Promise<string> {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    fetch(url, { credentials: 'include' })
      .then(async (res) => {
        if (!res.ok) throw new Error('下载失败')
        const blob = await res.blob()
        const a = document.createElement('a')
        const objectUrl = window.URL.createObjectURL(blob)
        a.href = objectUrl
        a.download = filename || url.split('/').pop() || 'download'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(objectUrl)
        resolve(a.download)
      })
      .catch((err) => {
        // 兜底：如果 fetch 失败，尝试直接跳转下载
        try {
          const a = document.createElement('a')
          a.href = url
          a.download = filename || url.split('/').pop() || 'download'
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          resolve(a.download)
        } catch {
          reject(err)
        }
      })
    // #endif

    // #ifndef H5
    uni.downloadFile({
      url,
      success: (res) => {
        if (res.statusCode === 200 && res.tempFilePath) {
          uni.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              resolve(saveRes.savedFilePath)
            },
            fail: (saveErr) => {
              reject(saveErr)
            },
          })
        } else {
          reject(res)
        }
      },
      fail: (err) => {
        reject(err)
      },
    })
    // #endif
  })
}

/**
 * @description 样式转换，对象转字符串，或者字符串转对象
 * @param {object | string} customStyle 需要转换的目标
 * @param {String} target 转换的目的，object-转为对象，string-转为字符串
 * @returns {object|string}
 */
export const addStyle = (customStyle: Record<string, any> | string, target: 'object' | 'string' = 'object') => {
  // 字符串转字符串，对象转对象情形，直接返回
  if (
    isEmpty(customStyle) ||
    (typeof customStyle === 'object' && target === 'object') ||
    (target === 'string' && typeof customStyle === 'string')
  ) {
    return customStyle
  }
  // 字符串转对象
  if (typeof customStyle === 'string') {
    // 去除字符串样式中的两端空格(中间的空格不能去掉，比如padding: 20px 0如果去掉了就错了)，空格是无用的
    customStyle = (customStyle as string).trim()
    // 根据";"将字符串转为数组形式
    const styleArray = customStyle.split(';')
    const style: Record<string, string> = {}
    // 历遍数组，拼接成对象
    for (let i = 0; i < styleArray.length; i++) {
      // 'font-size:20px;color:red;'，如此最后字符串有";"的话，会导致styleArray最后一个元素为空字符串，这里需要过滤
      if (styleArray[i]) {
        const item = styleArray[i].split(':')
        style[item[0].trim()] = item[1].trim()
      }
    }
    return style
  }
  // 这里为对象转字符串形式
  let string = ''
  if (typeof customStyle === 'object') {
    Object.entries(customStyle as Record<string, any>).forEach(([val, i]) => {
      // 驼峰转为中划线的形式，否则css内联样式，无法识别驼峰样式属性名
      const key = i.replace(/([A-Z])/g, '-$1').toLowerCase()
      string += `${key}:${val};`
    })
  }
  // 去除两端空格
  return string.trim()
}

/**
 * @description 添加单位，如果有rpx，upx，%，px等单位结尾或者值为auto，直接返回，否则加上px单位结尾
 * @param {string|number} value 需要添加单位的值
 * @param {string} unit 添加的单位名 比如px
 */
export const addUnit = (value: string | number = 'auto', unit = 'px') => {
  if (unit == 'rpx' && isNumber(value)) {
    value = value * 2
  }
  value = String(value)
  return isNumber(value) ? `${value}${unit}` : value
}

/**
 * @description 对象转url参数
 * @param {object} data,对象
 * @param {Boolean} isPrefix,是否自动加上"?"
 * @param {string} arrayFormat 规则 indices|brackets|repeat|comma
 */
export const queryParams = (data: Record<string, any> = {}, isPrefix = true, arrayFormat = 'brackets') => {
  const prefix = isPrefix ? '?' : ''
  const _result = []
  if (['indices', 'brackets', 'repeat', 'comma'].indexOf(arrayFormat) === -1) arrayFormat = 'brackets'
  for (const key in data) {
    const value = data[key]
    // 去掉为空的参数
    if (['', undefined, null].indexOf(value) >= 0) {
      continue
    }
    // 如果值为数组，另行处理
    if (value.constructor === Array) {
      // e.g. {ids: [1, 2, 3]}
      switch (arrayFormat) {
        case 'indices':
          // 结果: ids[0]=1&ids[1]=2&ids[2]=3
          for (let i = 0; i < value.length; i++) {
            _result.push(`${key}[${i}]=${value[i]}`)
          }
          break
        case 'brackets':
          // 结果: ids[]=1&ids[]=2&ids[]=3
          value.forEach((_value) => {
            _result.push(`${key}[]=${_value}`)
          })
          break
        case 'repeat':
          // 结果: ids=1&ids=2&ids=3
          value.forEach((_value) => {
            _result.push(`${key}=${_value}`)
          })
          break
        case 'comma':
          // 结果: ids=1,2,3
          let commaStr = ''
          value.forEach((_value) => {
            commaStr += (commaStr ? ',' : '') + _value
          })
          _result.push(`${key}=${commaStr}`)
          break
        default:
          value.forEach((_value) => {
            _result.push(`${key}[]=${_value}`)
          })
      }
    } else {
      _result.push(`${key}=${value}`)
    }
  }
  return _result.length ? prefix + _result.join('&') : ''
}

/**
 * @description 获取当前页面路径
 */
export const getCurrentPagePath = () => {
  const currentPage = getCurrentPages().at(-1)
  const route = currentPage?.route
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  const params = currentPage?.options
  const query = Object.keys(params)
    .reduce((acc, key) => acc + `${key}=${params[key]}&`, '?')
    .slice(0, -1)

  return '/' + route + query
}

/**
 * 保存图片到本地
 * @param {string} imgUrl - 图片URL或Base64
 * @param {string} filename - 保存的文件名
 */
export const saveImage = (imgUrl: string, filename: string = 'image'): Promise<void> => {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    // H5环境下载图片
    const saveImgToLocal = (dataUrl: string, fileName: string) => {
      const aLink = document.createElement('a')
      aLink.download = fileName
      aLink.href = dataUrl
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
      uni.showToast({
        title: '图片已保存',
        icon: 'success',
      })
    }

    // 处理图片URL
    if (imgUrl.indexOf('data:image') === 0) {
      // 已经是Base64格式
      saveImgToLocal(imgUrl, `${filename}_${Date.now()}.png`)
    } else {
      // 网络图片或本地图片路径，需要转换为Base64
      const image = new Image()
      image.crossOrigin = 'anonymous' // 解决跨域问题
      image.src = imgUrl
      image.onload = () => {
        const canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        const ctx = canvas.getContext('2d')
        ctx?.drawImage(image, 0, 0, image.width, image.height)
        const dataURL = canvas.toDataURL('image/png')
        saveImgToLocal(dataURL, `${filename}_${Date.now()}.png`)
        resolve()
      }
      image.onerror = () => {
        uni.showToast({
          title: '图片加载失败',
          icon: 'none',
        })
        reject('图片加载失败')
      }
    }
    // #endif

    // #ifdef APP-PLUS || MP
    // 非H5环境使用原生API
    uni.saveImageToPhotosAlbum({
      filePath: imgUrl,
      success: () => {
        uni.showToast({
          title: '保存成功',
          icon: 'success',
        })
        resolve()
      },
      fail: (err) => {
        console.log('🚀 ~ returnnewPromise ~ err:', err)
        reject(err)
      },
    })
    // #endif
  })
}

/**
 * H5环境下保存图片到手机相册
 * @param {string} imgUrl - 图片URL或Base64
 */
export const saveImageToAlbum = (imgUrl: string): void => {
  // #ifdef H5
  // 创建全屏预览层
  const previewContainer = document.createElement('div')
  previewContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  `

  // 创建图片元素
  const imgElement = document.createElement('img')
  imgElement.src = imgUrl
  imgElement.style.cssText = `
    max-width: 90%;
    max-height: 70%;
    object-fit: contain;
  `

  // 创建提示文本
  const tipElement = document.createElement('div')
  tipElement.innerText = '长按图片保存到相册'
  tipElement.style.cssText = `
    color: white;
    font-size: 16px;
    margin-top: 20px;
  `

  // 创建关闭按钮
  const closeButton = document.createElement('div')
  closeButton.innerText = '关闭'
  closeButton.style.cssText = `
    color: white;
    font-size: 16px;
    margin-top: 20px;
    padding: 10px 20px;
    border: 1px solid white;
    border-radius: 4px;
  `

  // 添加关闭事件
  closeButton.addEventListener('click', () => {
    document.body.removeChild(previewContainer)
  })

  // 组装预览层
  previewContainer.appendChild(imgElement)
  previewContainer.appendChild(tipElement)
  previewContainer.appendChild(closeButton)
  document.body.appendChild(previewContainer)

  // 点击空白区域关闭
  previewContainer.addEventListener('click', (e) => {
    if (e.target === previewContainer) {
      document.body.removeChild(previewContainer)
    }
  })

  // #endif

  // #ifdef APP-PLUS || MP
  // 非H5环境使用原生API
  uni.saveImageToPhotosAlbum({
    filePath: imgUrl,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    },
    fail: (err) => {
      if (err.errMsg.indexOf('auth deny') !== -1) {
        uni.openSetting({
          success: (settingRes) => {
            console.log('设置结果', settingRes)
          },
        })
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none',
        })
      }
    },
  })
  // #endif
}

/**
 * @description pdf 预览
 * @param url 文件地址
 * @param filename 文件名
 */
export function previewPDF(url: string, filename = 'PDF 预览') {
  let currentScale = 1
  let initialDistance = 0
  let initialScale = 1
  let pdfDoc: any = null
  let isPinching = false
  let rafId: number | null = null
  let pinchCenter = { x: 0, y: 0 } // 缩放中心点
  let lastScrollTop = 0 // 记录缩放前的滚动位置

  const container = document.createElement('div')
  container.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
    display: flex;
    flex-direction: column;
  `

  // 头部
  const header = document.createElement('div')
  header.style.cssText = `
    padding: 10px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `
  const title = document.createElement('span')
  title.style.cssText = `
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  `
  title.textContent = filename
  const closeBtn = document.createElement('button')
  closeBtn.textContent = '关闭'
  closeBtn.style.cssText = `
    padding: 5px 10px;
    background: #007aff;
    color: white;
    border: none;
    border-radius: 4px;
  `
  closeBtn.onclick = () => document.body.removeChild(container)

  // 内容区 - 修复滚动问题
  const content = document.createElement('div')
  content.style.cssText = `
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #f5f5f5;
    touch-action: pan-y pinch-zoom;
    height: 100%;
    position: relative;
    will-change: transform;
    -webkit-overflow-scrolling: touch;
  `
  const loading = document.createElement('div')
  loading.textContent = '正在加载PDF...'
  loading.style.cssText = `
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 16px;
  `
  content.appendChild(loading)

  header.appendChild(title)
  header.appendChild(closeBtn)
  container.appendChild(header)
  container.appendChild(content)
  document.body.appendChild(container)

  // 计算两点距离
  function getDistance(touch1: Touch, touch2: Touch) {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 计算两点中心
  function getCenter(touch1: Touch, touch2: Touch) {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    }
  }

  // 渲染所有页面
  const renderPages = async (scale = 1) => {
    if (!pdfDoc) return
    content.innerHTML = ''
    for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
      try {
        const page = await pdfDoc.getPage(pageNum)
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        const containerWidth = content.clientWidth || 600

        // 计算缩放
        const initialViewport = page.getViewport({ scale: 1 })
        const baseScale = containerWidth / initialViewport.width
        const devicePixelRatio = window.devicePixelRatio || 1
        const renderScale = baseScale * scale * devicePixelRatio
        const viewport = page.getViewport({ scale: renderScale })

        // 高分辨率渲染
        canvas.height = viewport.height
        canvas.width = viewport.width
        canvas.style.width = `${baseScale * scale * initialViewport.width}px`
        canvas.style.height = `${baseScale * scale * initialViewport.height}px`
        canvas.style.display = 'block'
        canvas.style.background = 'white'

        if (!context) continue
        context.scale(devicePixelRatio, devicePixelRatio)

        const renderContext = {
          canvasContext: context,
          viewport: page.getViewport({ scale: baseScale * scale }),
        }
        await page.render(renderContext).promise
        content.appendChild(canvas)
      } catch (error: any) {
        const errorDiv = document.createElement('div')
        errorDiv.textContent = `第${pageNum}页渲染失败: ${error.message}`
        errorDiv.style.cssText = `
          color: red;
          padding: 10px;
          margin: 10px;
          border: 1px solid red;
        `
        content.appendChild(errorDiv)
      }
    }
  }

  // 计算缩放后应该滚动到的位置
  function calculateNewScrollPosition(oldScale: number, newScale: number, centerY: number, oldScrollTop: number) {
    const scaleRatio = newScale / oldScale
    const headerHeight = header.offsetHeight
    const relativeY = centerY - headerHeight
    const contentRelativeY = relativeY + oldScrollTop
    const newContentRelativeY = contentRelativeY * scaleRatio
    const newScrollTop = newContentRelativeY - relativeY
    return Math.max(0, newScrollTop)
  }

  // 双指缩放事件
  content.addEventListener('touchstart', (e) => {
    if (e.touches.length === 2) {
      e.preventDefault()
      isPinching = true
      initialDistance = getDistance(e.touches[0], e.touches[1])
      initialScale = currentScale
      pinchCenter = getCenter(e.touches[0], e.touches[1])
      lastScrollTop = content.scrollTop
    }
  })

  content.addEventListener('touchmove', (e) => {
    if (e.touches.length === 2 && isPinching) {
      e.preventDefault()
      const currentDistance = getDistance(e.touches[0], e.touches[1])
      let scale = (currentDistance / initialDistance) * initialScale
      scale = Math.max(0.5, Math.min(3, scale))
      currentScale = scale

      // 只做视觉缩放，不重绘
      content.style.transform = `scale(${currentScale})`
      content.style.transformOrigin = 'center top'

      // 防抖：不频繁重绘
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => {
        // nothing, 只做视觉缩放
      })
    }
  })

  content.addEventListener('touchend', () => {
    if (isPinching) {
      isPinching = false
      const oldScale = initialScale
      const newScale = currentScale

      // 重置视觉缩放
      content.style.transform = ''

      // 重绘并保持缩放位置
      renderPages(currentScale).then(() => {
        // 计算新的滚动位置以保持缩放中心
        const newScrollTop = calculateNewScrollPosition(oldScale, newScale, pinchCenter.y, lastScrollTop)
        content.scrollTop = newScrollTop
      })
    }
  })

  // 加载 PDF
  pdfjsLib
    .getDocument(url)
    .promise.then((pdf) => {
      pdfDoc = pdf
      renderPages(currentScale)
    })
    .catch((error) => {
      content.innerHTML = `
        <div style="text-align: center; padding: 20px; color: #f00; font-size: 16px;">
          <h3>加载PDF失败</h3>
          <p>错误信息: ${error.message}</p>
          <p>URL: ${url}</p>
          <button onclick="window.open('${url}', '_blank')" style="padding: 10px 20px; background: #007aff; color: white; border: none; border-radius: 4px; margin-top: 10px;">
            尝试在新窗口打开
          </button>
        </div>
      `
    })
}
