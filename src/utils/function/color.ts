/**
 * @description 判断是否十六进制颜色值.
 * @param color 十六进制颜色值
 * @return
 */
export const isHexColor = (color: string) => {
  const reg = /^#(?:[0-9a-f]{3}|[0-9a-f]{6})$/i
  return reg.test(color)
}

/**
 * @description 将rgb表示方式转换为hex表示方式
 * @param rgb rgb颜色值
 * @returns
 */
export const rgbToHex = (rgb: string) => {
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  if (/^(rgb|RGB)/.test(rgb)) {
    const aColor = rgb.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',')
    let strHex = '#'
    for (let i = 0; i < aColor.length; i++) {
      let hex = Number(aColor[i]).toString(16)
      hex = String(hex).length == 1 ? `${0}${hex}` : hex // 保证每个rgb的值为2位
      if (hex === '0') {
        hex += hex
      }
      strHex += hex
    }
    if (strHex.length !== 7) {
      strHex = rgb
    }
    return strHex
  }
  if (reg.test(rgb)) {
    const aNum = rgb.replace(/#/, '').split('')
    if (aNum.length === 6) {
      return rgb
    }
    if (aNum.length === 3) {
      let numHex = '#'
      for (let i = 0; i < aNum.length; i += 1) {
        numHex += aNum[i] + aNum[i]
      }
      return numHex
    }
  } else {
    return rgb
  }
}

/**
 * @description JS颜色十六进制转换为rgb或rgba,返回的格式为 rgba（255，255，255，0.5）字符串
 * @param color 十六进制颜色值
 * @param alpha rgba的透明度
 * @returns
 */
export const colorToRgba = (color: string, alpha: number) => {
  color = rgbToHex(color)!
  // 十六进制颜色值的正则表达式
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  /* 16进制颜色转为RGB格式 */
  let sColor = String(color).toLowerCase()
  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      let sColorNew = '#'
      for (let i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
      }
      sColor = sColorNew
    }
    // 处理六位的颜色值
    const sColorChange = []
    for (let i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))
    }
    return `rgba(${sColorChange.join(',')},${alpha})`
  }

  return sColor
}
