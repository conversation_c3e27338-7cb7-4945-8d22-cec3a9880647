import dayjs from 'dayjs'

/**
 * 时间戳转换 或 获取当前时间的时间戳
 * @param timeStr 时间戳
 * @returns
 */
export function getTimeStamp(timeStr?: string | number) {
  if (!timeStr) return Date.now()
  let t = timeStr
  t = (t as number) > 0 ? +t : t.toString().replace(/-/g, '/')
  return new Date(t).getTime()
}

/**
 * @description 将时间字符串或时间戳转换成对应格式
 * @param timeStr
 * @param type 类型
 * @returns
 */
export const formatDate = (timeStr?: string | number, type = 'YYYY/MM/DD HH:mm'): string => {
  if (!timeStr) {
    const t = getTimeStamp()
    return dayjs(t).format(type)
  }
  const timestampRegex = /^\d{10}$|^\d{13}$/
  timeStr = String(timeStr)
  if (timestampRegex.test(timeStr)) {
    const t = timeStr.length === 10 ? Number.parseInt(`${timeStr}000`, 10) : Number.parseInt(timeStr, 10)
    return dayjs(t).format(type)
  }
  return dayjs(timeStr).format(type)
}
