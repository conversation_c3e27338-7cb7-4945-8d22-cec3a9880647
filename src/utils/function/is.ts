import { isArray, isObject, isString } from 'lodash-es'

export {
  isBoolean,
  isDate,
  isFunction,
  isNull,
  isNumber,
  isRegExp,
  isString,
  isUndefined,
  isArray,
  isObject,
} from 'lodash-es'

export const isEmpty = <T = unknown>(val: T): val is T => {
  if (isArray(val) || isString(val)) {
    return val.length === 0
  }
  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }
  if (isObject(val)) {
    return Object.keys(val).length === 0
  }
  return false
}
