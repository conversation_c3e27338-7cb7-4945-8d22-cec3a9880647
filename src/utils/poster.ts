/**
 * 海报生成工具类
 */

// 海报元素类型定义
export interface PosterElement {
  type: 'rect' | 'text' | 'image' | 'line' | 'circle';
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  text?: string;
  src?: string;
  color?: string;
  backgroundColor?: string;
  borderRadius?: number;
  borderWidth?: number;
  borderColor?: string;
  fontSize?: number;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right';
  lineHeight?: number;
  maxWidth?: number;
  maxLine?: number;
  startX?: number;
  startY?: number;
  endX?: number;
  endY?: number;
  lineWidth?: number;
  radius?: number;
}

// 海报配置
export interface PosterConfig {
  canvasId: string;
  width: number;
  height: number;
  elements: PosterElement[];
}

export default {
  /**
   * 绘制海报
   * @param config 海报配置
   * @param callback 回调函数
   */
  async drawPoster(config: PosterConfig, callback: (path: string | null) => void): Promise<void> {
    try {
      const { canvasId, width, height, elements } = config
      
      // 创建canvas上下文
      const ctx = uni.createCanvasContext(canvasId)
      
      // 绘制背景
      ctx.fillStyle = '#FFFFFF'
      ctx.fillRect(0, 0, width, height)
      
      // 按顺序绘制元素
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i]
        await this.drawElement(ctx, element)
      }
      
      // 绘制完成
      ctx.draw(false, () => {
        setTimeout(() => {
          // 导出图片
          uni.canvasToTempFilePath({
            canvasId,
            success: (res) => {
              callback && callback(res.tempFilePath)
            },
            fail: (err) => {
              console.error('导出海报失败', err)
              callback && callback(null)
            }
          })
        }, 300) // 延迟确保绘制完成
      })
    } catch (error) {
      console.error('绘制海报出错', error)
      callback && callback(null)
    }
  },
  
  /**
   * 绘制元素
   */
  async drawElement(ctx: any, element: PosterElement): Promise<void> {
    const { type } = element
    
    switch (type) {
      case 'rect':
        this.drawRect(ctx, element)
        break
      case 'text':
        this.drawText(ctx, element)
        break
      case 'image':
        await this.drawImage(ctx, element)
        break
      case 'line':
        this.drawLine(ctx, element)
        break
      case 'circle':
        this.drawCircle(ctx, element)
        break
    }
  },
  
  /**
   * 绘制矩形
   */
  drawRect(ctx: any, element: PosterElement): void {
    const { x = 0, y = 0, width = 0, height = 0, backgroundColor, borderRadius, borderWidth, borderColor } = element
    
    ctx.save()
    
    if (borderRadius) {
      // 绘制圆角矩形
      this.drawRoundRect(ctx, x, y, width, height, borderRadius)
      
      if (backgroundColor) {
        ctx.fillStyle = backgroundColor
        ctx.fill()
      }
      
      if (borderWidth && borderColor) {
        ctx.lineWidth = borderWidth
        ctx.strokeStyle = borderColor
        ctx.stroke()
      }
    } else {
      // 绘制普通矩形
      if (backgroundColor) {
        ctx.fillStyle = backgroundColor
        ctx.fillRect(x, y, width, height)
      }
      
      if (borderWidth && borderColor) {
        ctx.lineWidth = borderWidth
        ctx.strokeStyle = borderColor
        ctx.strokeRect(x, y, width, height)
      }
    }
    
    ctx.restore()
  },
  
  /**
   * 绘制圆角矩形
   */
  drawRoundRect(ctx: any, x: number, y: number, width: number, height: number, radius: number): void {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.arcTo(x + width, y, x + width, y + radius, radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius)
    ctx.lineTo(x + radius, y + height)
    ctx.arcTo(x, y + height, x, y + height - radius, radius)
    ctx.lineTo(x, y + radius)
    ctx.arcTo(x, y, x + radius, y, radius)
    ctx.closePath()
  },
  
  /**
   * 绘制文本
   */
  drawText(ctx: any, element: PosterElement): void {
    const { 
      text = '', 
      x = 0, 
      y = 0, 
      fontSize = 14, 
      color = '#000000', 
      textAlign = 'left', 
      fontWeight = 'normal',
      lineHeight,
      maxWidth,
      maxLine
    } = element
    
    ctx.save()
    ctx.fillStyle = color
    ctx.textAlign = textAlign
    ctx.textBaseline = 'top'
    ctx.font = `${fontWeight} ${fontSize}px sans-serif`
    
    if (maxWidth) {
      // 处理文本换行
      this.drawTextWrap(ctx, text, x, y, maxWidth, lineHeight || fontSize * 1.5, maxLine)
    } else {
      ctx.fillText(text, x, y)
    }
    
    ctx.restore()
  },
  
  /**
   * 绘制换行文本
   */
  drawTextWrap(ctx: any, text: string, x: number, y: number, maxWidth: number, lineHeight: number, maxLine?: number): void {
    if (!text) return
    
    const textArr = text.split('')
    let line = ''
    let lineNum = 1
    
    for (let i = 0; i < textArr.length; i++) {
      let testLine = line + textArr[i]
      let metrics = ctx.measureText(testLine)
      let testWidth = metrics.width
      
      if (testWidth > maxWidth && i > 0) {
        ctx.fillText(line, x, y)
        line = textArr[i]
        y += lineHeight
        lineNum++
        
        if (maxLine && lineNum > maxLine) {
          if (i < textArr.length - 1) {
            ctx.fillText(line + '...', x, y)
          } else {
            ctx.fillText(line, x, y)
          }
          break
        }
      } else {
        line = testLine
      }
      
      if (i === textArr.length - 1) {
        ctx.fillText(line, x, y)
      }
    }
  },
  
  /**
   * 绘制图片
   */
  async drawImage(ctx: any, element: PosterElement): Promise<void> {
    return new Promise((resolve) => {
      const { src = '', x = 0, y = 0, width = 0, height = 0, borderRadius } = element
      
      // 处理网络图片
      if (src.startsWith('http')) {
        uni.downloadFile({
          url: src,
          success: (res) => {
            if (res.statusCode === 200) {
              this.drawImageContent(ctx, res.tempFilePath, x, y, width, height, borderRadius)
              resolve()
            } else {
              console.error('下载图片失败', res)
              resolve()
            }
          },
          fail: (err) => {
            console.error('下载图片出错', err)
            resolve()
          }
        })
      } else {
        // 本地图片
        this.drawImageContent(ctx, src, x, y, width, height, borderRadius)
        resolve()
      }
    })
  },
  
  /**
   * 绘制图片内容
   */
  drawImageContent(ctx: any, src: string, x: number, y: number, width: number, height: number, borderRadius?: number): void {
    if (borderRadius) {
      // 绘制圆角图片
      ctx.save()
      this.drawRoundRect(ctx, x, y, width, height, borderRadius)
      ctx.clip()
      ctx.drawImage(src, x, y, width, height)
      ctx.restore()
    } else {
      // 绘制普通图片
      ctx.drawImage(src, x, y, width, height)
    }
  },
  
  /**
   * 绘制线条
   */
  drawLine(ctx: any, element: PosterElement): void {
    const { startX = 0, startY = 0, endX = 0, endY = 0, lineWidth = 1, color = '#000000' } = element
    
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(startX, startY)
    ctx.lineTo(endX, endY)
    ctx.lineWidth = lineWidth
    ctx.strokeStyle = color
    ctx.stroke()
    ctx.restore()
  },
  
  /**
   * 绘制圆形
   */
  drawCircle(ctx: any, element: PosterElement): void {
    const { x = 0, y = 0, radius = 0, backgroundColor, borderWidth, borderColor } = element
    
    ctx.save()
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, 2 * Math.PI)
    
    if (backgroundColor) {
      ctx.fillStyle = backgroundColor
      ctx.fill()
    }
    
    if (borderWidth && borderColor) {
      ctx.lineWidth = borderWidth
      ctx.strokeStyle = borderColor
      ctx.stroke()
    }
    
    ctx.restore()
  },
  
  /**
   * 保存图片到相册
   */
  saveImageToAlbum(filePath: string, callback?: (success: boolean) => void): void {
    // 保存图片到相册
    uni.saveImageToPhotosAlbum({
      filePath,
      success: () => {
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        callback && callback(true)
      },
      fail: (err) => {
        if (err.errMsg.indexOf('auth deny') !== -1) {
          this.showAuthModal()
        } else {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
        callback && callback(false)
      }
    })
  },
  
  /**
   * 显示授权提示弹窗
   */
  showAuthModal(): void {
    uni.showModal({
      title: '提示',
      content: '需要您授权保存图片到相册',
      confirmText: '去授权',
      success: (res) => {
        if (res.confirm) {
          uni.openSetting({
            success: (settingRes) => {
              console.log('设置结果', settingRes)
            }
          })
        }
      }
    })
  }
}