/**
 * @description: 获取BaseUrl
 * @returns {string}
 */
export const getEnvBaseUrl = (): string => {
  return import.meta.env.VITE_SERVER_BASEURL
}

/**
 * @description: 获取文件上传BaseUrl
 * @returns {string}
 */
export const getEnvBaseUploadUrl = (): string => {
  return import.meta.env.VITE_UPLOAD_BASEURL
}

/**
 * @description: 获取图片BaseUrl
 * @returns {string}
 */
export const getImageBaseUrl = (): string => {
  return getEnvBaseUrl() + '/uploads/'
}
