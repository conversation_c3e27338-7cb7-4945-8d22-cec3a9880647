<script lang="ts" setup>
import { isArray, isEmpty, aesDecrypt } from '@/utils'
import { useTelephoneStore, useVoteStore } from '@/stores'
import { uploadFileApi } from '@/api/common'
import { submitMobileVoteApi } from '@/api/telephone'
import type { VoteMatterItem } from '@/api/vote/types.ts'
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import type { MobileVoteSubmitParams } from '@/api/telephone/types.ts'
import { JtStatusEnum, MobileVoteStatusEnum } from '@/api/telephone/types.ts'
import FooterButton from '@/components/footer-button/footer-button.vue'

const query = defineProps<{
  id: string
  uuid: string
}>()

const { voteDetail } = storeToRefs(useVoteStore())
const { fetchVoteDetailAction } = useVoteStore()

const { mobileList, voteCallRecords, voteCallStatus } = storeToRefs(useTelephoneStore())
const { fetchVoteCallRecordsAction, fetchCallStatusByTicketAction } = useTelephoneStore()

const jsStatus = [
  { value: JtStatusEnum.CONNECTED, label: '接通' },
  { value: JtStatusEnum.NO_ANSWER, label: '无人接听' },
  { value: JtStatusEnum.EMPTY_NUMBER, label: '空号' },
  { value: JtStatusEnum.HANG_UP, label: '挂断' },
]

const voteStatus = [
  { value: MobileVoteStatusEnum.VOTED, label: '已投' },
  { value: MobileVoteStatusEnum.ABSTAINED, label: '弃权' },
  { value: MobileVoteStatusEnum.NOT_VOTED, label: '未表态' },
]

const showPhonePicker = ref(false)
// 是否显示加载动画
const showLoading = ref(false)
// 投票入参
const voteFormParams = ref<MobileVoteSubmitParams>({
  topic_id: '',
  pqbm: '',
  name: '',
  mobile: '',
  jt_status: '',
  dhtp_status: '',
  item: [],
  voice: [],
})
const currentMobileItem = computed(() => mobileList.value.find((item) => item.id === +query.id)!)

const userList = computed(() => {
  return currentMobileItem.value.user.map((item) => ({ text: `${aesDecrypt(item.name)} ${aesDecrypt(item.mobile)}` }))
})

const userInputValue = computed(() => {
  if (!voteFormParams.value.name && !voteFormParams.value.mobile) {
    return ''
  } else {
    return `${voteFormParams.value.name} ${voteFormParams.value.mobile}`
  }
})

watch(
  () => currentMobileItem.value.user,
  (newVal) => {
    if (newVal.length === 1) {
      voteFormParams.value.name = aesDecrypt(newVal[0].name)
      voteFormParams.value.mobile = aesDecrypt(newVal[0].mobile)
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => voteCallStatus.value,
  (newVal) => {
    if (!newVal) return
    voteFormParams.value.jt_status = newVal.jt_status
    voteFormParams.value.dhtp_status = newVal.dhtp_status
  },
)

const getJtStatusText = (status: JtStatusEnum) => {
  return jsStatus.find((item) => item.value === status)?.label
}
const getVoteStatusText = (status: MobileVoteStatusEnum) => {
  return voteStatus.find((item) => item.value === status)?.label
}

const isChecked = (id: number, optionId: string) => {
  return voteFormParams.value.item.find((item) => item.id === id)?.option_id?.includes(optionId)
}

const handlePhoneInputClick = () => {
  if (currentMobileItem.value.user.length === 1) return
  showPhonePicker.value = true
}
const handlePhonePickerChange = (event: any) => {
  const [name, mobile] = event.text.split(' ')
  voteFormParams.value.mobile = mobile
  voteFormParams.value.name = name
}
// 接通状态改变
const handleJtStatusChange = () => {
  voteFormParams.value.dhtp_status = ''
  voteFormParams.value.item = []
}
// 投票状态改变
const handleVoteStatusChange = () => {
  voteFormParams.value.item = []
}
// 选项改变
const handleOptionChange = (event: any, item: any) => {
  const value = event.detail.value
  const index = voteFormParams.value.item.findIndex((voteItem) => item.id === voteItem.id)
  const isChecked = index !== -1
  const voteItem: VoteMatterItem = {
    id: item.id,
    type: item.type,
    option_id: isArray(value) ? value : [value],
  }
  if (!isChecked) return voteFormParams.value.item.push(voteItem)
  if (isEmpty(value)) return voteFormParams.value.item.splice(index, 1)
  voteFormParams.value.item[index] = voteItem
}
// 上传录音
const handleUpload = () => {
  uni.chooseFile({
    count: 1,
    success: async ({ tempFiles }) => {
      try {
        uni.showLoading({ title: '上传中...', mask: true })
        const { name, filepath } = await uploadFileApi({
          name: 'file',
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          filePath: tempFiles[0]?.path,
        })
        voteFormParams.value.voice.push({ name, url: filepath })
        uni.hideLoading()
      } catch (err) {
        uni.hideLoading()
        uni.showToast({ title: '上传失败，请稍后再试', icon: 'none' })
        console.log('uploadFileApi error', err)
      }
    },
    fail: (err) => {
      console.log('err', err)
    },
  })
}
// 删除录音
const handleVoiceDelete = (index: number) => {
  voteFormParams.value.voice.splice(index, 1)
}

// 提交投票
const handleSubmit = async () => {
  try {
    showLoading.value = true
    await submitMobileVoteApi({
      ...voteFormParams.value,
      pqbm: currentMobileItem.value.pqbm,
      topic_id: currentMobileItem.value.topic_id,
    })
    setTimeout(() => {
      showLoading.value = false
      uni.navigateBack()
    }, 800)
  } catch (error: any) {
    console.log('error', error)
    showLoading.value = false
  }
}

onLoad(() => {
  fetchVoteDetailAction(query.uuid)
  fetchVoteCallRecordsAction(currentMobileItem.value.pqbm)
  fetchCallStatusByTicketAction(currentMobileItem.value.pqbm)
})
</script>

<template>
  <view class="write-wrapper bg-no-repeat px-3.5 overflow-hidden">
    <view
      class="mt-5.5 px-3 pt-2 pb-4 bg-white rounded-lg"
      style="background: linear-gradient(180deg, #ffede7 -1%, #ffffff 34%)"
    >
      <view class="flex items-center gap-x-2">
        <image class="size-7" src="@/static/icons/house.png" />
        <view class="font-350 text-lg text-[#EF4F3F]">房屋信息</view>
      </view>
      <view class="mt-4 text-base">{{ aesDecrypt(currentMobileItem.address) }}</view>
      <view class="mt-2 text-base">面积：{{ currentMobileItem.area }}㎡</view>
    </view>
    <view class="my-3 pb-3.5 bg-white rounded-lg overflow-hidden">
      <tui-form>
        <tui-form-item :bottom-border="false" label="拨打电话" label-size="36" padding="28rpx 24rpx">
          <template #row>
            <view class="bg-white px-[30rpx]">
              <tui-input
                :border-bottom="false"
                :model-value="userInputValue"
                :radius="8"
                background-color="#f7f7f7"
                disabled
                padding="20rpx 24rpx"
                placeholder="请选择"
                placeholder-style="font-weight: 350; font-size: 32rpx; color: #999999"
                @click="handlePhoneInputClick"
              >
                <template #right>
                  <tui-icon :bold="false" color="#999" name="arrowdown" size="22" />
                </template>
              </tui-input>
            </view>
          </template>
        </tui-form-item>
        <tui-form-item :bottom-border="false" label="接通状态" label-size="36" padding="28rpx 24rpx">
          <template #row>
            <tui-radio-group v-model="voteFormParams.jt_status" class="flex flex-col gap-y-4 px-[30rpx]">
              <tui-label
                v-for="(item, index) in jsStatus"
                :key="index"
                class="flex items-center gap-x-3.5"
                @tap="handleJtStatusChange()"
              >
                <tui-radio :value="item.value"></tui-radio>
                <text class="font-350 text-base">{{ item.label }}</text>
              </tui-label>
            </tui-radio-group>
          </template>
        </tui-form-item>

        <view v-if="voteFormParams.jt_status === JtStatusEnum.CONNECTED">
          <tui-form-item :bottom-border="false" label="投票状态" label-size="36" padding="28rpx 24rpx">
            <template #row>
              <tui-radio-group v-model="voteFormParams.dhtp_status" class="flex flex-col gap-y-4 px-[30rpx]">
                <tui-label
                  v-for="(item, index) in voteStatus"
                  :key="index"
                  class="flex items-center gap-x-3.5"
                  @tap="handleVoteStatusChange"
                >
                  <tui-radio :value="item.value"></tui-radio>
                  <text class="font-350 text-base">{{ item.label }}</text>
                </tui-label>
              </tui-radio-group>
            </template>
          </tui-form-item>
          <tui-form-item
            v-if="voteFormParams.dhtp_status === MobileVoteStatusEnum.VOTED"
            :bottom-border="false"
            label="投票结果"
            label-size="36"
          >
            <template #row>
              <view class="px-3.5 pb-4 flex flex-col gap-y-7">
                <view v-for="(item, index) in voteDetail.items" :key="index">
                  <view class="mb-2 text-[#EF4F3F] text-base text-center font-500">
                    事项{{ index + 1 }}：{{ item.title }}</view
                  >
                  <view class="mt-4">
                    <!-- 单选 -->
                    <template v-if="item.type === VoteOptionsTypeEnum.SINGLE">
                      <tui-radio-group @change="(e: any) => handleOptionChange(e, item)">
                        <view class="flex flex-col gap-y-2.5">
                          <tui-label v-for="(option, i) in item.option_arr" :key="i">
                            <view
                              class="flex items-center justify-between px-3 h-10 rounded"
                              :class="
                                isChecked(item.id, option.id) ? 'bg-[#FA483B]/10 text-[#FA483B]' : 'bg-neutral-100'
                              "
                            >
                              <text>{{ option.content }}</text>
                              <tui-radio :value="option.id"> </tui-radio>
                            </view>
                          </tui-label>
                        </view>
                      </tui-radio-group>
                    </template>
                    <!-- 多选 -->
                    <template v-else>
                      <tui-checkbox-group @change="(e: any) => handleOptionChange(e, item)">
                        <view class="flex flex-col gap-y-2.5">
                          <tui-label v-for="(option, i) in item.option_arr" :key="i">
                            <view
                              :class="
                                isChecked(item.id, option.id) ? 'bg-[#FA483B]/10 text-[#FA483B]' : 'bg-neutral-100'
                              "
                              class="flex items-center justify-between px-3 h-10 rounded"
                            >
                              <text>{{ option.content }}</text>
                              <tui-checkbox :value="option.id"> </tui-checkbox>
                            </view>
                          </tui-label>
                        </view>
                      </tui-checkbox-group>
                    </template>
                  </view>
                </view>
              </view>
            </template>
          </tui-form-item>
          <tui-form-item :bottom-border="false" label="录音文件" label-size="36">
            <template #row>
              <view class="px-[30rpx] pb-3">
                <view
                  v-for="(voice, index) in voteFormParams.voice"
                  :key="index"
                  class="relative mb-3 flex items-center px-4 gap-x-3 py-3 bg-[#F7F7F7] rounded-lg"
                >
                  <view class="absolute -top-1.5 -right-1">
                    <tui-icon name="close" size="24" @click="handleVoiceDelete(index)" />
                  </view>
                  <image class="size-5 shrink-0" mode="aspectFill" src="@/static/icons/file.png" />
                  <view class="text-base line-clamp-2">{{ voice.name }}</view>
                </view>
                <view
                  class="flex items-center justify-center gap-x-2 py-3 rounded-lg border border-solid border-neutral-100 text-[#999]"
                  @tap="handleUpload"
                >
                  <view class="i-fluent:add-20-filled text-base"></view>
                  <text class="text-base">上传录音文件</text>
                </view>
              </view>
            </template>
          </tui-form-item>
        </view>
        <tui-form-item v-if="voteCallRecords.length" :bottom-border="false" label="历史记录" label-size="36">
          <template #row>
            <view v-for="item in voteCallRecords" :key="item.id" class="px-[30rpx]">
              <view v-if="item.showTime" class="flex items-center gap-x-2.5 mt-3 mb-2.5">
                <view class="size-3 bg-[#F05040] rounded-full"></view>
                <text class="text-sm text-[#666]">{{ item.bd_time }}</text>
              </view>
              <view class="p-3 flex flex-col gap-y-2.5 bg-[#f7f7f7] rounded-lg mb-3">
                <view class="flex items-center text-base">
                  <text class="text-[#999999] w-20">接通状态：</text>
                  <text>{{ getJtStatusText(item.jt_status) }}</text>
                </view>
                <view class="flex items-center text-base">
                  <text class="text-[#999999] w-20">投票状态：</text>
                  <text>{{ getVoteStatusText(item.dhtp_status) || '--' }}</text>
                </view>
              </view>
            </view>
          </template>
        </tui-form-item>
      </tui-form>
    </view>
    <FooterButton @click="handleSubmit">保存</FooterButton>
  </view>

  <!--  加载动画-->
  <tui-loading v-if="showLoading" is-mask></tui-loading>

  <tui-picker
    :pickerData="userList"
    :show="showPhonePicker"
    @change="handlePhonePickerChange"
    @hide="showPhonePicker = false"
  >
  </tui-picker>
</template>

<style lang="scss" scoped>
.write-wrapper {
  min-height: 100%;
  background-size: 100% 340rpx;
  background-image: linear-gradient(180deg, #fb653a 37%, rgba(249, 98, 54, 0.81) 69%, rgba(255, 110, 69, 0) 91%);
  background-color: #f7f7f7;
}
</style>
