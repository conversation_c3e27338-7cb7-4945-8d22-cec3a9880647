<script setup lang="ts">
import { switchCommunityApi } from '@/api/telephone'
import { useTelephoneStore } from '@/stores'

const { communityList } = storeToRefs(useTelephoneStore())
const { fetchCommunityListAction } = useTelephoneStore()

const switchCommunity = async (e: any, id: number) => {
  await switchCommunityApi(id)
  uni.navigateTo({ url: '/pages/telephone/index' })
}

onLoad(() => {
  fetchCommunityListAction()
})
</script>

<template>
  <tui-list-view color="#777">
    <tui-list-cell
      v-for="item in communityList"
      :key="item.id"
      :hover="true"
      :arrow="true"
      padding="33rpx"
      @click="(e: any) => switchCommunity(e, item.id)"
    >
      {{ item.name }}
    </tui-list-cell>
  </tui-list-view>
</template>

<style lang="scss" scoped></style>
