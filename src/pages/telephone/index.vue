<script lang="ts" setup>
import { getAssetsImages } from '@/utils'
import Wait from './components/Wait.vue'
import Complete from './components/Complete.vue'
import Profile from './components/Profile.vue'

const tabBar = [
  {
    text: '待拨打',
    iconPath: getAssetsImages('tabbar_wait.png'),
    selectedIconPath: getAssetsImages('tabbar_wait_active.png'),
    iconSize: '48rpx',
    component: Wait,
  },
  {
    text: '已拨打',
    iconPath: getAssetsImages('tabbar_complete.png'),
    selectedIconPath: getAssetsImages('tabbar_complete_active.png'),
    iconSize: '48rpx',
    component: Complete,
  },
  {
    text: '个人中心',
    iconPath: getAssetsImages('tabbar_user.png'),
    selectedIconPath: getAssetsImages('tabbar_user_active.png'),
    iconSize: '48rpx',
    component: Profile,
  },
]

const currentIndex = ref(0)
</script>

<template>
  <view :class="currentIndex === 2 ? 'bg-white' : 'bg-[#ef4f3f]'" class="flex flex-col h-full">
    <view class="flex-1 overflow-hidden">
      <KeepAlive>
        <component :is="tabBar[currentIndex].component"></component>
      </KeepAlive>
    </view>

    <view class="tabbar flex bg-white rounded-t-4.5 pb-safe">
      <view
        v-for="(item, index) in tabBar"
        :key="index"
        :class="{ 'text-[#fa483b]': index === currentIndex }"
        class="flex-1 flex flex-col items-center text-[#999] py-2.5"
        @tap="currentIndex = index"
      >
        <image
          :src="index === currentIndex ? item.selectedIconPath : item.iconPath"
          class="w-5.5 h-6 mb-1"
          mode="aspectFill"
        ></image>
        <text class="text-xs">{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.tabbar {
  box-shadow: 0px -4px 12px 0px rgba(205, 205, 205, 0.25);
}
</style>
