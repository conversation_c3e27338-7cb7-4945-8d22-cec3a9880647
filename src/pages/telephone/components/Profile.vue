<script lang="ts" setup>
import { useUserStore } from '@/stores'
import { getCallRecordsApi } from '@/api/telephone'
import { JtStatusEnum, MobileVoteStatusEnum } from '@/api/telephone/types.ts'
import { groupBy } from 'lodash-es'

const { userInfo } = storeToRefs(useUserStore())
const { logoutAction } = useUserStore()

const ZPagingRef = ref<ZPagingInstance | null>(null)
const callRecords = ref<any>([])
const callTotal = ref(0)

const fetchCallRecords = async (page: number, limit: number) => {
  const { data, total } = await getCallRecordsApi({ page, limit })
  const result = data.map((item: any) => ({ ...item, bd_time: item.bd_time.split(' ')[0] }))
  const groupResult = groupBy(result, 'bd_time')

  const newCallRecords = []
  for (const [key, value] of Object.entries(groupResult)) {
    if (key !== callRecords.value.at(-1)?.bd_time) {
      value[0].showTime = true
    }
    newCallRecords.push(...value)
  }

  callTotal.value = total
  ZPagingRef.value?.complete(newCallRecords)
}
const refreshCallRecords = () => {
  callRecords.value = []
}

const getJtStatusText = (status: JtStatusEnum) => {
  switch (status) {
    case JtStatusEnum.CONNECTED:
      return '接通'
    case JtStatusEnum.HANG_UP:
      return '挂断'
    case JtStatusEnum.NO_ANSWER:
      return '无人接听'
    case JtStatusEnum.EMPTY_NUMBER:
      return '空号'
  }
}
const getVoteStatusText = (status: MobileVoteStatusEnum) => {
  switch (status) {
    case MobileVoteStatusEnum.VOTED:
      return '已投'
    case MobileVoteStatusEnum.NOT_VOTED:
      return '未表态'
    case MobileVoteStatusEnum.ABSTAINED:
      return '弃权'
  }
}
// 退出登录
const logout = () => {
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    confirmColor: '#FA483B',
    success: ({ confirm }) => {
      if (!confirm) return
      logoutAction()
      uni.$t.route({
        type: 'reLaunch',
        url: '/pages/login/worker',
      })
    },
  })
}

onActivated(() => {
  ZPagingRef.value?.reload()
})
</script>

<template>
  <view class="size-full" style="background: linear-gradient(176deg, #ff6e45 1%, #f35224 25%)">
    <view class="profile-wrapper h-full flex flex-col pt-15 bg-no-repeat bg-[length:100%_422rpx]">
      <view class="flex items-center gap-x-3.5 px-4 mb-6">
        <image class="size-14" src="@/static/images/avatar.png" />
        <text class="font-bold text-xl text-white">{{ userInfo?.name }}</text>
      </view>

      <view class="bg-white rounded-t-2.5 p-3.5 flex-1 flex flex-col overflow-hidden">
        <view class="py-1 font-500 text-lg text-[#333] mb-2">
          <text>已拨打次数</text>
          <text class="text-[#EF5040]">（{{ callTotal }}次）</text>
        </view>
        <view class="flex-1">
          <z-paging
            ref="ZPagingRef"
            v-model="callRecords"
            :auto="false"
            :auto-clean-list-when-reload="false"
            :fixed="false"
            height="100%"
            @onRefresh="refreshCallRecords"
            @query="fetchCallRecords"
          >
            <view v-for="item in callRecords" :key="item.id">
              <view v-if="item.showTime" class="flex items-center gap-x-2.5 mt-3 mb-2.5">
                <view class="size-3 bg-[#F05040] rounded-full"></view>
                <text class="text-sm text-[#666]">{{ item.bd_time }}</text>
              </view>
              <view class="p-3 flex flex-col gap-y-2.5 bg-[#f7f7f7] rounded-lg mb-3">
                <view class="flex items-center text-base">
                  <text class="text-[#999999] w-20">业主姓名：</text>
                  <text>{{ item.user_name }}</text>
                </view>
                <view class="flex items-center text-base">
                  <text class="text-[#999999] w-20">房间号：</text>
                  <text>{{ item.address }}</text>
                </view>
                <view class="flex items-center text-base">
                  <text class="text-[#999999] w-20">接通状态：</text>
                  <text>{{ getJtStatusText(item.jt_status) }}</text>
                </view>
                <view v-if="item.jt_status === JtStatusEnum.CONNECTED" class="flex items-center text-base">
                  <text class="text-[#999999] w-20">投票状态：</text>
                  <text>{{ getVoteStatusText(item.dhtp_status) }}</text>
                </view>
              </view>
            </view>
            <template #bottom>
              <tui-form-button @click="logout">退出登录</tui-form-button>
            </template>
          </z-paging>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.profile-wrapper {
  background-image: url('@/static/images/tel_profile_bg.png');
}

:deep(.tui-button__container) {
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.2);
}
</style>
