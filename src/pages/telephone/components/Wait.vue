<script lang="ts" setup>
import { useTelephoneStore } from '@/stores'
import { getMobileListApi } from '@/api/telephone'
import Search from '@/components/search/search.vue'
import TelCard from './TelCard.vue'
import { CallStatusEnum } from '@/api/telephone/types.ts'

const { mobileList } = storeToRefs(useTelephoneStore())

const ZPagingRef = ref<ZPagingInstance | null>(null)

const searchValue = ref('')

const fetchMobileList = async (page: number, limit: number) => {
  const { data } = await getMobileListApi({ page, limit, is_call: CallStatusEnum.UN_CALL, keyword: searchValue.value })
  ZPagingRef.value?.complete(data)
}
const handleSearchInput = () => {
  ZPagingRef.value?.reload()
}

onActivated(() => {
  ZPagingRef.value?.reload()
})
</script>

<template>
  <view class="wait-wrapper-inner px-3.5 bg-no-repeat bg-[length:100%_520rpx]">
    <z-paging
      ref="ZPagingRef"
      v-model="mobileList"
      :auto="false"
      :fixed="false"
      height="100%"
      loading-more-theme-style="white"
      refresher-theme-style="white"
      @query="fetchMobileList"
    >
      <template #top>
        <!--      标题-->
        <view class="pt-11 mb-7 font-500 text-6.5 text-white">电话投票-待拨打</view>

        <!--      搜索框-->
        <Search
          v-model="searchValue"
          debounce
          placeholder="搜索姓名/手机号"
          search-class="backdrop-blur-sm bg-white/90!"
          @input="handleSearchInput"
        />
      </template>

      <!--      列表-->
      <view class="pt-3 flex flex-col gap-y-3">
        <TelCard v-for="item in mobileList" :key="item.id" :data="{ ...item, callStatus: CallStatusEnum.UN_CALL }" />
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.wait-wrapper-inner {
  height: 100%;
  background-image: url('@/static/images/offline_tel_bg.png');
}
</style>
