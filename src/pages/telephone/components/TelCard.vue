<script lang="ts" setup>
import { CallStatusEnum, JtStatusEnum, type MobileListItem, MobileVoteStatusEnum } from '@/api/telephone/types'
import { getAssetsImages, aesDecrypt } from '@/utils'

interface Data extends MobileListItem {
  callStatus: CallStatusEnum
}

const props = defineProps({
  data: {
    type: Object as PropType<Data>,
    required: true,
    default: () => ({}),
  },
})

const showActionSheet = ref(false)

const itemList = computed(() => {
  return props.data.user.map((user) => ({
    text: `${aesDecrypt(user.name)} ${aesDecrypt(user.mobile)}`,
  }))
})

const getVoteStatusImage = (status: MobileVoteStatusEnum) => {
  switch (status) {
    case MobileVoteStatusEnum.VOTED:
      return getAssetsImages('vote_complete.png')
    case MobileVoteStatusEnum.NOT_VOTED:
      return getAssetsImages('vote_no_declare.png')
    case MobileVoteStatusEnum.ABSTAINED:
      return getAssetsImages('vote_abstention.png')
    default:
      return ''
  }
}

const getJtStatusImage = (status: JtStatusEnum) => {
  switch (status) {
    case JtStatusEnum.CONNECTED:
      return getAssetsImages('connected.png')
    case JtStatusEnum.NO_ANSWER:
      return getAssetsImages('no_answer.png')
    case JtStatusEnum.EMPTY_NUMBER:
      return getAssetsImages('empty_number.png')
    case JtStatusEnum.HANG_UP:
      return getAssetsImages('hang_up.png')
  }
}

const handleActionSheetClick = (e: any) => {
  uni.makePhoneCall({
    phoneNumber: e.text.split(' ').at(-1),
  })
  showActionSheet.value = false
}
const navigateToPage = (url: string) => {
  uni.navigateTo({ url })
}
</script>

<template>
  <view class="relative flex gap-x-2 p-3.5 bg-white rounded-lg">
    <!-- 状态 -->
    <image
      v-if="data.jt_status === JtStatusEnum.CONNECTED"
      :src="getVoteStatusImage(data.dhtp_status)"
      class="absolute top-0 right-3 w-[108rpx] h-[50rpx]"
      mode="aspectFill"
    />

    <image
      v-if="data.callStatus === CallStatusEnum.UN_CALL"
      class="size-11.5 rounded-full shrink-0"
      src="@/static/images/avatar_1.png"
    />

    <image v-else :src="getJtStatusImage(data.jt_status)" class="size-11.5 rounded-full shrink-0" />

    <view class="flex-1 text-sm text-[#666]">
      <view class="text-base text-[#333]">{{ aesDecrypt(data.address) }}</view>
      <view v-for="(u, index) in data.user" :key="index">
        {{ aesDecrypt(u.name) }}
        <tui-text :size="28" :text="aesDecrypt(u.mobile)" color="#333" format text-type="mobile"></tui-text>
      </view>
      <view class="mt-3 flex justify-between">
        <view class="py-1 px-4.5 flex items-center gap-x-1 bg-[#FDEDEB] rounded-full">
          <image class="size-4" src="@/static/icons/edit.png" />
          <text
            class="text-[#FA483B]"
            @tap="navigateToPage(`/pages/telephone/write?uuid=${data.topic_uuid}&id=${data.id}`)"
          >
            填写记录
          </text>
        </view>
        <view
          class="py-1 px-4.5 flex items-center gap-x-1 rounded-full"
          style="background: linear-gradient(107deg, #ff7f51 10%, #ef4f3f 93%)"
          @tap="showActionSheet = true"
        >
          <image class="size-4" src="@/static/icons/tel.png" />
          <text class="text-white">快速拨打</text>
        </view>
      </view>
    </view>
  </view>

  <teleport to="body">
    <tui-actionsheet
      :item-list="itemList"
      :show="showActionSheet"
      text-color="#333"
      tips="请选择拨打的业主"
      @cancel="showActionSheet = false"
      @click="handleActionSheetClick"
    >
    </tui-actionsheet>
  </teleport>
</template>

<style scoped></style>
