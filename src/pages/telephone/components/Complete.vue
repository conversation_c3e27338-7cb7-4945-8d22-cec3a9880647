<script lang="ts" setup>
import Search from '@/components/search/search.vue'
import TelCard from './TelCard.vue'
import { useTelephoneStore } from '@/stores'
import { getMobileListApi } from '@/api/telephone'
import { CallStatusEnum } from '@/api/telephone/types.ts'

const { mobileList } = storeToRefs(useTelephoneStore())

const ZPagingRef = ref<ZPagingInstance | null>(null)

const searchValue = ref('')

const fetchMobileList = async (page: number, limit: number) => {
  const { data } = await getMobileListApi({ page, limit, is_call: CallStatusEnum.CALL, keyword: searchValue.value })
  ZPagingRef.value?.complete(data)
}

onActivated(() => {
  ZPagingRef.value?.reload()
})
</script>

<template>
  <view class="complete-wrapper-inner px-3.5 bg-no-repeat bg-[length:100%_520rpx]">
    <z-paging
      ref="ZPagingRef"
      v-model="mobileList"
      :auto="false"
      :fixed="false"
      height="100%"
      loading-more-theme-style="white"
      refresher-theme-style="white"
      @query="fetchMobileList"
    >
      <template #top>
        <!--      标题-->
        <view class="pt-11 mb-7 font-500 text-6.5 text-white">电话投票-已拨打</view>

        <!--      搜索框-->
        <Search
          v-model="searchValue"
          bg-color="rgba(255,255,255,0.9)"
          placeholder="搜索姓名/手机号"
          search-class="backdrop-blur-sm"
          @input="() => ZPagingRef?.reload()"
        />
      </template>

      <!--      列表-->
      <view class="pt-3 flex flex-col gap-y-3">
        <TelCard v-for="item in mobileList" :key="item.id" :data="{ ...item, callStatus: CallStatusEnum.CALL }" />
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.complete-wrapper-inner {
  height: 100%;
  background-image: url('@/static/images/offline_tel_bg.png');
}
</style>
