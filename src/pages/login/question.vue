<script lang="ts" setup>
import FooterButton from '@/components/footer-button/footer-button.vue'
import { useUserStore } from '@/stores'
import type { LoginQuestionParams } from '@/api/user/types'

const query = defineProps<{
  form: string
}>()

const { loginQuestionAction } = useUserStore()

const questionForm = ref<LoginQuestionParams>(JSON.parse(query.form))

// 身份验证（登录）
const handleLogin = async () => {
  uni.showLoading({
    title: '登录中',
    mask: true,
  })
  await loginQuestionAction(questionForm.value)
  uni.hideLoading()
  uni.navigateTo({
    url: '/pages/question/index',
  })
}
</script>

<template>
  <view class="login-wrapper bg-[length:100%_542rpx] bg-no-repeat bg-center-top">
    <view class="px-6.5 pt-8">
      <view class="flex items-center gap-x-3 text-white mb-4.5">
        <text class="text-[52rpx] font-500">问卷调查</text>
        <image class="w-[144rpx] h-[52rpx]" src="@/static/images/login_identity_tag.png" />
      </view>
    </view>

    <view class="bg-white rounded-t-xl px-6 pt-6 mt-14">
      <view class="flex gap-x-9 text-lg font-500"> </view>

      <view class="py-18 text-center">
        <view class="mb-6 text-base text-[#666]">检测到您当前登录的手机号为:</view>
        <tui-text
          :size="44"
          :text="questionForm.mobile"
          align="center"
          block
          color="#F13939"
          font-weight="500"
          format
          text-type="mobile"
        ></tui-text>
      </view>
    </view>

    <FooterButton @click="handleLogin"> 登录 </FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.login-wrapper {
  width: 100vw;
  min-height: 100vh;
  background-image: url('@/static/images/login_bg.png');
}
</style>
