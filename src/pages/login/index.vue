<script lang="ts" setup>
import { useUserStore, useVoteStore } from '@/stores'
import { LoginChannelEnum, type LoginVerifyCodeParams, ScanCodeTypeEnum } from '@/api/user/types.ts'
import FooterButton from '@/components/footer-button/footer-button.vue'
import { getTopicVerifyTypeApi, scanCodeLoginApi, sendVerifyCodeApi } from '@/api/user/login'
import { base64Decode } from '@/pages/login/utils.ts'

const query = defineProps<{
  phone?: string
  channel?: LoginChannelEnum
  pqbm?: string
  topic_id?: string
  way?: ScanCodeTypeEnum
}>()

const { voteParams } = storeToRefs(useVoteStore())
const { loginTicketCodeAction, loginVerifyCodeAction, logoutAction } = useUserStore()

const tabsData = ['业主信息验证']

// 当前选择的tab索引
const selectTabIndex = ref(query.way && Number(query.way) === ScanCodeTypeEnum.一户一码 ? 1 : 0)
// 验证码发送成功的值
const verifyCodeSuccessVal = ref(0)
// 验证码登录参数
const verifyCodeForm = ref<LoginVerifyCodeParams>({
  mobile: query.phone ? base64Decode(query.phone) : '',
  code: '',
  channel: Number(query.channel),
})
// 票权编码
const pqbm = ref(query.pqbm || '')
// 票权登录是否需要手机号
const isPqbmNeedPhone = ref(false)

// 其他系统跳转过来
const otherApp = computed(() => query.channel && Number(query.channel) === LoginChannelEnum.MINI_PROGRAM)

// 是否手机号直接登录
const isPhoneLogin = computed(() => query.phone && otherApp.value)
// // 手机验证码登录
const isVerifyCodeLogin = computed(() => !query.phone && !otherApp.value)
// 票权登录
const isTicketLogin = computed(() => !!query.pqbm && otherApp.value)

onLoad(async () => {
  // 清除token
  logoutAction()

  if (query.topic_id || query.pqbm) {
    const data = await getTopicVerifyTypeApi(query?.topic_id || query?.pqbm || '')
    isPqbmNeedPhone.value = data.includes('4')
    console.log('isPqbmNeedPhone', isPqbmNeedPhone.value)
  }

  if (!query.way) return
  voteParams.value.way = Number(query.way)
  if (Number(query.way) === ScanCodeTypeEnum.一户一码) {
    await loginTicketCodeAction(pqbm.value)
    scanLogin()
    uni.navigateTo({
      url: '/pages/index/index',
    })
  }
})

// 发送验证码
const handleVerifyCodeSend = async () => {
  await sendVerifyCodeApi(verifyCodeForm.value.mobile)
  verifyCodeSuccessVal.value++
}
// 身份验证（登录）
const handleLogin = async () => {
  uni.showLoading({
    title: '登录中',
    mask: true,
  })
  if (selectTabIndex.value === 0) {
    // 验证码登录
    await loginVerifyCodeAction(verifyCodeForm.value)
  } else {
    const mobile = isPqbmNeedPhone.value && query.phone ? query.phone : undefined
    // 票权验证
    await loginTicketCodeAction(pqbm.value, mobile)
  }
  scanLogin()
  uni.hideLoading()
  uni.navigateTo({
    url: '/pages/index/index',
  })
}

async function scanLogin() {
  // 扫码登录
  if (query.channel && Number(query.channel) === LoginChannelEnum.MINI_PROGRAM && query.way && query.topic_id) {
    await scanCodeLoginApi({
      mobile: verifyCodeForm.value.mobile,
      type: Number(query.way),
      topic_id: query.topic_id,
    })
  }
}
</script>

<template>
  <view class="login-wrapper bg-[length:100%_542rpx] bg-no-repeat bg-center-top">
    <view class="px-6.5 pt-8">
      <view class="flex items-center gap-x-3 text-white mb-4.5">
        <text class="text-[52rpx] font-500">业主大会投票</text>
        <image class="w-[144rpx] h-[52rpx]" src="@/static/images/login_identity_tag.png" />
      </view>
    </view>

    <view class="bg-white rounded-t-xl px-6 pt-6 mt-14">
      <view class="flex gap-x-9 text-lg font-500">
        <text
          v-for="(tab, index) in tabsData"
          :key="index"
          :class="selectTabIndex === index && 'text-[#F13939]'"
          @tap="selectTabIndex = index"
        >
          {{ tab }}
        </text>
      </view>

      <!-- 手机号直接验证登录-->
      <view v-if="isPhoneLogin" class="py-18 text-center">
        <view class="mb-6 text-base text-[#666]">检测到您当前登录的手机号为:</view>
        <tui-text
          :size="44"
          :text="base64Decode(phone!)"
          align="center"
          block
          color="#F13939"
          font-weight="500"
          format
          text-type="mobile"
        ></tui-text>
      </view>
      <!-- 手机号+验证码验证登录 -->
      <view v-if="isVerifyCodeLogin" class="form pt-10 px-2">
        <view class="form-item mb-10">
          <view class="flex items-center gap-x-2">
            <image class="w-4.5 h-5" mode="aspectFill" src="@/static/icons/login_mobile.png" />
            <text>手机号</text>
          </view>
          <view class="form-item__content">
            <tui-input
              v-model="verifyCodeForm.mobile"
              :lineLeft="false"
              maxlength="11"
              padding="28rpx 54rpx"
              placeholder="请输入您的手机号"
              placeholderStyle="color: rgba(0, 0, 0, 0.3); font-size: 28rpx;"
              type="tel"
            ></tui-input>
          </view>
        </view>
        <view class="form-item">
          <view class="flex items-center gap-x-2">
            <image class="w-4.5 h-5" mode="aspectFill" src="@/static/icons/login_code.png" />
            <text>验证码</text>
          </view>
          <view class="form-item__content">
            <tui-input
              v-model="verifyCodeForm.code"
              :lineLeft="false"
              padding="28rpx 0 28rpx 54rpx"
              placeholder="请输入您的验证码"
              placeholderStyle="color: rgba(0, 0, 0, 0.3); font-size: 28rpx;"
              type="numeric"
            >
              <template #right>
                <tui-countdown-verify
                  :seconds="60"
                  :success-val="verifyCodeSuccessVal"
                  border-color="#EA5247"
                  color="#EA5247"
                  radius="100rpx"
                  @send="handleVerifyCodeSend"
                ></tui-countdown-verify>
              </template>
            </tui-input>
          </view>
        </view>
      </view>
      <!-- 票权验证-->
      <view v-if="isTicketLogin" class="form pt-10 px-2">
        <view class="form-item">
          <view class="flex items-center gap-x-2">
            <image class="w-4.5 h-5" mode="aspectFill" src="@/static/icons/login_code.png" />
            <text>票权验证</text>
          </view>
          <view class="form-item__content">
            <tui-input
              v-model="pqbm"
              :lineLeft="false"
              padding="28rpx 0 28rpx 54rpx"
              placeholder="请输入您的票权"
              placeholderStyle="color: rgba(0, 0, 0, 0.3); font-size: 28rpx;"
              type="numeric"
            >
            </tui-input>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="fixed px-6.5 w-full bottom-25 bg-white">
      <view class="font-350 text-center text-[#EA5247]"> 使用其他手机号 </view>
    </view> -->

    <FooterButton @click="handleLogin"> 身份验证 </FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.login-wrapper {
  width: 100vw;
  min-height: 100vh;
  background-image: url('@/static/images/login_bg.png');
}
</style>
