<script lang="ts" setup>
import { debounce } from 'lodash-es'
import { useUserStore } from '@/stores'
import type { LoginWorkerParams } from '@/api/user/types.ts'
import FooterButton from '@/components/footer-button/footer-button.vue'

const query = defineProps<{ redirect?: string }>()

console.log('url', query.redirect && decodeURIComponent(query.redirect))

const { loginWorkerAction } = useUserStore()

const formRef = ref<any>()

const loginParams = ref<LoginWorkerParams>({
  mobile: '',
  password: '',
})

const rules = [
  {
    name: 'mobile',
    rule: ['required', 'isMobile'],
    msg: ['请输入手机号', '请输入正确的手机号'],
  },
  {
    name: 'password',
    rule: ['required'],
    msg: ['请输入密码'],
  },
]

const handleLogin = debounce(async () => {
  const { errorMsg, isPass } = await formRef.value?.validate(loginParams.value, rules)
  if (!isPass) {
    return uni.showToast({
      title: errorMsg,
      icon: 'error',
      mask: true,
    })
  }
  uni.showLoading({ title: '登录中...', mask: true })
  await loginWorkerAction(loginParams.value)
  uni.hideLoading()
  if (query.redirect) {
    uni.$t.route({ type: 'reLaunch', url: decodeURIComponent(query.redirect) })
  } else {
    uni.$t.route({ type: 'reLaunch', url: '/pages/telephone/index' })
  }
}, 300)
</script>

<template>
  <view class="login-wrapper bg-[length:100%_542rpx] bg-no-repeat bg-center-top">
    <view class="px-6.5 pt-8">
      <view class="flex items-center gap-x-3 text-white mb-4.5">
        <text class="text-[52rpx] font-500">业主大会话务员登录</text>
        <!--        <image class="w-[144rpx] h-[52rpx]" src="@/static/images/login_identity_tag.png" />-->
      </view>
    </view>

    <view class="bg-white rounded-t-xl px-6 pt-6 mt-14">
      <tui-form
        ref="formRef"
        :duration="3000"
        :model="loginParams"
        :show-message="false"
        padding="80rpx 16rpx 0"
        tip-background-color="#ff7900"
      >
        <view class="form-item mb-10">
          <view class="flex items-center gap-x-2">
            <image class="w-4.5 h-5" mode="aspectFill" src="@/static/icons/login_mobile.png" />
            <text>手机号</text>
          </view>
          <view class="form-item__content">
            <tui-input
              v-model="loginParams.mobile"
              :lineLeft="false"
              maxlength="11"
              padding="28rpx 54rpx"
              placeholder="请输入您的手机号"
              placeholderStyle="color: rgba(0, 0, 0, 0.3); font-size: 28rpx;"
              type="tel"
            ></tui-input>
          </view>
        </view>
        <view class="form-item">
          <view class="flex items-center gap-x-2">
            <image class="w-4.5 h-5" mode="aspectFill" src="@/static/icons/login_password.png" />
            <text>密码</text>
          </view>
          <view class="form-item__content">
            <tui-input
              v-model="loginParams.password"
              :lineLeft="false"
              padding="28rpx 0 28rpx 54rpx"
              password
              placeholder="请输入密码"
              placeholderStyle="color: rgba(0, 0, 0, 0.3); font-size: 28rpx;"
            >
            </tui-input>
          </view>
        </view>
      </tui-form>
    </view>

    <FooterButton @click="handleLogin"> 登录 </FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.login-wrapper {
  width: 100vw;
  min-height: 100vh;
  background-image: url('@/static/images/login_bg.png');
}
</style>
