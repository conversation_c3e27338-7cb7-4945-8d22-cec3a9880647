export const base64Decode = (str: string) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  let output = ''
  let buffer = 0
  let bufLength = 0

  for (let i = 0; i < str.length; i++) {
    if (str[i] === '=') break // 遇到填充字符停止
    buffer = (buffer << 6) | chars.indexOf(str[i])
    bufLength += 6
    while (bufLength >= 8) {
      output += String.fromCharCode((buffer >> (bufLength - 8)) & 0xff)
      bufLength -= 8
    }
  }

  return output
}
