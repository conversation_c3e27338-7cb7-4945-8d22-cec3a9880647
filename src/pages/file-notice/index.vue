<script lang="ts" setup>
import { getImageBaseUrl, getAssetsImages } from '@/utils'
import { getFileInfoApi } from '@/api/common/index'
import FooterButton from '@/components/footer-button/footer-button.vue'
import FileCard from '@/components/file-card/file-card.vue'

const query = defineProps<{
  topic_id?: string
  id?: string
}>()

const info = ref<any>()

onLoad(() => {
  getInfo()
})

const getInfo = async () => {
  const res = await getFileInfoApi(query)
  info.value = res
  if (!res) {
    uni.showToast({
      title: '暂无数据',
      icon: 'error',
      duration: 5000,
    })
  }
}

const handleSubmit = () => {
  if (query.id) {
    window.location.href = info.value.topic.url
  } else {
    uni.navigateBack()
  }
}
</script>

<template>
  <view
    class="bg-#f7f7f7 min-h-screen"
    :style="{
      backgroundImage: `url(${getAssetsImages('file-notice.png')})`,
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
    }"
  >
    <view v-if="info" class="p-[30rpx]">
      <view v-if="info.topic" class="px-[32rpx] py-[36rpx] top-title">
        {{ info.topic_title }}
      </view>
      <view class="bg-#FFF7F6 pb-4 rounded-8rpx">
        <view class="w-full px-[32rpx] py-[36rpx] bg-#fff rounded-10rpx mt-4">
          <view class="text-#333 font-700 mb-4">{{ info.name }}</view>
          <FileCard
            v-for="(item, index) in info.files"
            :key="index"
            :name="item.name"
            :url="getImageBaseUrl() + item.filepath"
            class="mb-2"
          ></FileCard>
          <view class="text-#999 text-24rpx">{{ info.created_at }} 发布</view>
        </view>

        <view class="w-full px-[32rpx] py-[36rpx] bg-#fff rounded-10rpx mt-4" v-if="info.video?.length">
          <view class="text-#333 font-700 mb-4">{{ info.name }}</view>
          <template v-for="(item, index) in info.video" :key="index">
            <video :src="getImageBaseUrl() + item.filepath" controls class="w-full h-400rpx rounded-16rpx mb-2"></video>
            <view class="text-#999 text-24rpx">{{ item.date }} 发布</view>
          </template>
        </view>
      </view>
    </view>

    <FooterButton v-if="info.topic" @click="handleSubmit">前往投票</FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.top-title {
  border-radius: 18rpx;
  background: rgba(255, 255, 255, 0.2);
  box-sizing: border-box;
  border: 1rpx solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.05) 100%) 1;
  backdrop-filter: blur(8rpx);
  color: #fff8bb;
  text-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
  font-size: 36rpx;
  font-weight: bold;
}
</style>
