<script lang="ts" setup>
import { useUserStore, useVoteStore } from '@/stores'
import { getMyVoteListApi } from '@/api/ticket'
import VoteCard from '@/components/vote-card/vote-card.vue'
import tuiText from '@/components/thorui/tui-text/tui-text.vue'
import { type VoteLisItem, VoteProgressEnum, VoteStatusEnum } from '@/api/ticket/types.ts'

const { userInfo } = storeToRefs(useUserStore())
const { voteList, currentVoteId, touristVoteParams, voteParams } = storeToRefs(useVoteStore())

const ZPagingRef = ref<ZPagingInstance | null>(null)

// 获取投票列表
const fetchVoteList = async (page: number, limit: number) => {
  const { data } = await getMyVoteListApi({ page, limit })
  ZPagingRef.value?.complete(data)
}
onLoad((options: any) => {
  if (options.way) {
    touristVoteParams.value.way = Number(options.way)
    voteParams.value.way = Number(options.way)
  }
})

const navigateToVotePage = (item: VoteLisItem) => {
  currentVoteId.value = item.id
  // 投票结束后，跳转到公示页面
  if (item.is_start === VoteProgressEnum.FINISHED) {
    return uni.navigateTo({
      url: `/pages/vote/announcement?uuid=${item.uuid}`,
    })
  }

  if (item.status === VoteStatusEnum.VOTED) {
    // 未投票，跳转到投票页面
    uni.navigateTo({
      url: `/pages/vote/result?uuid=${item.uuid}`,
    })
  } else {
    // 已投票，跳转到投票结果页面
    uni.navigateTo({
      url: `/pages/vote/front?uuid=${item.uuid}`,
    })
  }
}

onShow(() => {
  ZPagingRef.value?.reload()
})
</script>

<template>
  <view class="home-wrapper bg-no-repeat bg-[length:100%_466rpx] bg-center-top">
    <z-paging
      ref="ZPagingRef"
      v-model="voteList"
      :empty-view-title-style="{
        fontSize: '36rpx',
        color: '#000',
      }"
      :fixed="false"
      :refresher-enabled="false"
      :show-refresher-when-reload="false"
      empty-view-text="请先领取票权"
      height="100%"
      @query="fetchVoteList"
    >
      <view class="flex items-center gap-x-3 px-3.5 mt-6">
        <view class="size-13.5 rounded-full border-4 border-solid border-white overflow-hidden">
          <image class="size-full" mode="aspectFill" src="@/static/images/avatar.png" />
        </view>
        <view class="flex flex-col justify-between">
          <view class="flex items-center gap-x-2">
            <text class="text-lg">{{ userInfo?.name }}</text>
          </view>
          <tuiText :text="userInfo?.mobile" color="#757270" format size="28rpx" textType="mobile"></tuiText>
        </view>
      </view>

      <!-- 投票列表 -->
      <view class="pt-7">
        <view class="flex items-center mb-3 px-3.5">
          <image class="size-6" mode="aspectFill" src="@/static/icons/free.png" />
          <text class="font-500 text-lg mt-0.5">投票列表</text>
        </view>

        <view class="flex flex-col gap-y-3">
          <VoteCard
            v-for="item in voteList"
            :key="item.id"
            :community-name="item.community_name"
            :end-time="item.e_time_str"
            :is-voted="item.status === VoteStatusEnum.VOTED"
            :start-time="item.s_time_str"
            :status="item.is_start"
            :title="item.title"
            :vote-count="item.join_num"
            @tap="navigateToVotePage(item)"
          />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.home-wrapper {
  height: 100vh;
  background-color: #f5f5f5;
  background-image: url('@/static/images/home_bg.png');
}
</style>
