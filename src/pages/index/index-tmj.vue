<script lang="ts" setup>
import { getAssetsImages } from '@/utils'
import { useUserStore } from '@/stores'
import { IsPassCheckEnum } from '@/api/user/types'
import { getTouristTopicListApi } from '@/api/tourist'
import { VoteProgressEnum } from '@/api/ticket/types.ts'
import VoteCard from '@/components/vote-card/vote-card.vue'
import tuiText from '@/components/thorui/tui-text/tui-text.vue'

const { userInfo } = storeToRefs(useUserStore())
const { touristLoginAction } = useUserStore()

const ZPagingRef = ref<ZPagingInstance | null>(null)

const loading = ref(true)
const voteList = ref<any[]>([])

// 获取投票列表
const fetchVoteList = async (page: number, limit: number) => {
  const { data } = await getTouristTopicListApi({ page, limit })
  ZPagingRef.value?.complete(data)
}

const navigateToVotePage = (item: any) => {
  // 投票结束后，跳转到公示页面
  if (item.is_start === VoteProgressEnum.FINISHED) {
    return uni.navigateTo({
      url: `/pages/vote/result-tmj?id=${item.id}&original=1`,
    })
  }

  if (item.is_join === 1) {
    // 已投票，跳转到投票结果页面
    uni.navigateTo({
      url: `/pages/vote/result-tmj?id=${item.id}&original=1`,
    })
  } else {
    // 未投票，跳转到投票页面
    uni.navigateTo({
      url: `/pages/vote/detail-tmj?id=${item.id}`,
    })
  }
}

onShow(() => {
  ZPagingRef.value?.reload()
})

onLoad(async (options) => {
  if (options?.userInfo) {
    const optionsUserInfo = JSON.parse(options?.userInfo || '{}')
    await touristLoginAction(formatLoginParams(optionsUserInfo))
    if (userInfo.value?.is_pass_check === IsPassCheckEnum.金标) {
      uni.navigateTo({
        url: '/pages/house/list',
      })
    }
  }
  loading.value = false
})

// 处理登录参数
function formatLoginParams(userInfo: any) {
  if (!userInfo) return

  const loginParams = {
    name: userInfo.realname,
    mobile: userInfo.phone,
    idcard: userInfo.id_card,
    community: userInfo.communities.map((item: any) => {
      return {
        name: item.community_name,
        uuid: item.community_code,
        house: item.ownerships.length
          ? item.ownerships.map((h: any) => {
              return {
                floor: h.building_no,
                unit: h.unit_no,
                room: h.house_no,
                address: h.house_name,
                area: h.area,
              }
            })
          : [],
      }
    }),
  }
  return loginParams
}
</script>

<template>
  <view v-if="!loading" class="home-wrapper bg-no-repeat bg-[length:100%_466rpx] bg-center-top">
    <z-paging
      ref="ZPagingRef"
      v-model="voteList"
      :empty-view-title-style="{
        fontSize: '36rpx',
        color: '#000',
      }"
      :fixed="false"
      :refresher-enabled="false"
      :show-refresher-when-reload="false"
      height="100%"
      @query="fetchVoteList"
    >
      <view class="flex items-center gap-x-3 px-3.5 mt-6">
        <view class="size-13.5 rounded-full border-4 border-solid border-white overflow-hidden">
          <image class="size-full" mode="aspectFill" src="@/static/images/avatar.png" />
        </view>
        <view class="flex flex-col justify-between">
          <view class="flex items-center gap-x-2">
            <text class="text-lg">{{ userInfo?.name }}</text>
            <image
              v-if="userInfo?.is_pass_check"
              :src="
                getAssetsImages(`${userInfo.is_pass_check === IsPassCheckEnum.金标 ? '业主1_icon' : '业主2_icon'}.png`)
              "
              style="width: 130rpx"
              mode="widthFix"
            />
          </view>
          <tuiText textType="mobile" :text="userInfo?.mobile" format size="28rpx" color="#757270"></tuiText>
        </view>
      </view>

      <!-- 投票列表 -->
      <view class="pt-7">
        <view class="flex items-center mb-3 px-3.5">
          <image class="size-6" mode="aspectFill" src="@/static/icons/free.png" />
          <text class="font-500 text-lg mt-0.5">投票列表</text>
        </view>

        <view class="flex flex-col gap-y-3">
          <VoteCard
            v-for="item in voteList"
            :key="item.id"
            :community-name="item.community"
            :end-time="item.e_time"
            :start-time="item.s_time"
            :status="item.is_start"
            :title="item.title"
            :vote-count="item.join_num"
            :is-voted="item.is_join === 1"
            @tap="navigateToVotePage(item)"
          />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.home-wrapper {
  height: 100vh;
  background-color: #f5f5f5;
  background-image: url('@/static/images/home_bg.png');
}
</style>
