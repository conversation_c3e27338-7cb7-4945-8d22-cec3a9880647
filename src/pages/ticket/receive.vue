<script lang="ts" setup>
import { useVoteStore } from '@/stores'
import { receiveTicketApi } from '@/api/ticket'
import FooterButton from '@/components/footer-button/footer-button.vue'
import ModalSuccess from '@/components/modal-success/modal-success.vue'

const { ticketList } = storeToRefs(useVoteStore())
const { fetchTicketListAction } = useVoteStore()

// 选择的投票码
const selectVoteCode = ref<string[]>([])
const showModal = ref(false)

const disabled = computed(() => !selectVoteCode.value.length)

// 判断是否选中
const isSelected = (code: string) => {
  return selectVoteCode.value.includes(code)
}
// 领取票权
const handleConfirm = async () => {
  await receiveTicketApi(selectVoteCode.value)
  showModal.value = true
}
// 返回首页
const navigateHomePage = () => {
  uni.redirectTo({
    url: '/pages/index/index',
  })
}
// 参与投票
const navigateToVotePage = () => {
  uni.navigateTo({ url: '/pages/ticket/my' })
}

onLoad(() => {
  fetchTicketListAction()
})
</script>

<template>
  <view class="receive-wrapper bg-no-repeat bg-center-top bg-[length:100%_416rpx]">
    <view class="pt-42">
      <view class="px-2">
        <image class="w-full h-3.5" mode="aspectFill" src="@/static/images/vote_receive_line.png" />
      </view>
      <view class="relative -top-3 px-3.5 z-1">
        <view class="w-full h-3 bg-gradient-to-b from-[#FFD7CC] from-5% to-[#FFDCCC]/0 to-98%"></view>
        <view class="relative -top-0.5 pt-3.5 px-3 pb-9 bg-white rounded-b-lg z-3">
          <view class="mb-4.5 font-700 text-xl text-[#3D3D3D] text-center">票权信息</view>
          <tui-checkbox-group v-model="selectVoteCode">
            <view class="flex flex-col gap-y-3.5">
              <tui-label v-for="(item, index) in ticketList" :key="index">
                <view
                  :class="isSelected(item.pqbm) && 'receive-item'"
                  class="px-4 pb-5 bg-[#FDF6F3] rounded-lg border border-solid border-transparent"
                >
                  <view
                    class="receive-item__title mb-2 h-7.5 font-700 leading-7.5 text-lg text-[#EF4629] text-center bg-image"
                  >
                    票权{{ index + 1 }}
                  </view>
                  <view class="flex items-center">
                    <view class="flex-1 flex flex-col gap-y-1">
                      <view class="text-base font-500 text-[#EF4629]">票权编码：{{ item.pqbm }}</view>
                      <view>
                        {{ item.floor_name }}-{{ item.unit_name }}-{{ item.room_name }} 房屋专有部分面积：{{
                          item.area
                        }}㎡
                      </view>
                      <view>业主信息：{{ item.name }} {{ item.mobile }}</view>
                    </view>
                    <tui-checkbox :scaleRatio="1" :value="item.pqbm" color="#FA483B"> </tui-checkbox>
                  </view>
                  <view class="mt-3 text-center text-base text-black font-500">{{ item.title }}</view>
                </view>
              </tui-label>
            </view>
          </tui-checkbox-group>
        </view>
        <view class="mt-3 mb-9 px-6 font-350 text-[#EF4F3F]">
          *为了业主大会的真实性，票权编码是您投票的有效凭证，请妥善保管，勿随意泄露。
        </view>
      </view>
    </view>
    <FooterButton :disabled="disabled" @click="handleConfirm">确认领取</FooterButton>

    <modal-success v-model="showModal" title="已成功领取您的票权">
      <template #bottom>
        <tui-form-button color="#F55545" height="80rpx" plain width="240rpx" @click="navigateHomePage">
          返回首页
        </tui-form-button>
        <tui-form-button height="80rpx" width="240rpx" @click="navigateToVotePage"> 参与投票 </tui-form-button>
      </template>
    </modal-success>
  </view>
</template>

<style lang="scss" scoped>
.receive-wrapper {
  min-height: 100vh;
  background-color: #fff7f6;
  background-image: url('@/static/images/vote_receive_bg.png');
}

.receive-item {
  border-color: #ff801f !important;
  box-shadow: 0 4px 10px 0 rgba(255, 97, 44, 0.37);
  &__title {
    background-image: url('@/static/images/vote_receive_title.png');
  }
}
</style>
