<script lang="ts" setup>
import { useVoteStore } from '@/stores'
import { getMyVoteListApi } from '@/api/ticket'
import { type VoteLisItem, VoteProgressEnum, VoteStatusEnum } from '@/api/ticket/types'
import VoteCard from '@/components/vote-card/vote-card.vue'

const query = defineProps<{
  index?: string
}>()

const { voteList, currentVoteId } = storeToRefs(useVoteStore())

const ZPagingRef = ref<ZPagingInstance | null>(null)

const tabsData = [
  {
    name: '未投票',
    value: VoteStatusEnum.UN_VOTE,
  },
  {
    name: '已投票',
    value: VoteStatusEnum.VOTED,
  },
]
const currentTab = ref<number>(query.index ? Number(query.index) : 0)

const fetchMyVoteList = async (page: number, limit: number) => {
  const status = tabsData[currentTab.value].value
  const { data } = await getMyVoteListApi({ page, limit, status })
  ZPagingRef.value?.complete(data)
}
const handleTabsChange = (e: any) => {
  currentTab.value = e.index
  ZPagingRef.value?.reload()
}
const navigateToVotePage = (item: VoteLisItem) => {
  currentVoteId.value = item.id
  // 投票结束后，跳转到公示页面
  if (item.is_start === VoteProgressEnum.FINISHED) {
    return uni.navigateTo({
      url: `/pages/vote/announcement`,
    })
  }

  if (item.status === VoteStatusEnum.VOTED) {
    // 未投票，跳转到投票页面
    uni.navigateTo({
      url: `/pages/vote/result?uuid=${item.uuid}`,
    })
  } else {
    // 已投票，跳转到投票结果页面
    uni.navigateTo({
      url: `/pages/vote/front?uuid=${item.uuid}`,
    })
  }
}
</script>

<template>
  <z-paging ref="ZPagingRef" v-model="voteList" bg-color="#f5f5f5" @query="fetchMyVoteList">
    <template #top>
      <tui-tabs
        :currentTab="currentTab"
        :height="100"
        :size="32"
        :tabs="tabsData"
        itemWidth="50%"
        unlined
        @change="handleTabsChange"
      ></tui-tabs>
    </template>
    <view class="flex flex-col gap-y-3 pt-2.5">
      <VoteCard
        v-for="item in voteList"
        :key="item.id"
        :community-name="item.community_name"
        :end-time="item.e_time_str"
        :is-voted="item.status === VoteStatusEnum.VOTED"
        :start-time="item.s_time_str"
        :status="item.is_start"
        :title="item.title"
        :vote-count="item.join_num"
        @tap="navigateToVotePage(item)"
      />
    </view>
  </z-paging>
</template>

<style lang="scss" scoped></style>
