<script lang="ts" setup>
import { useVoteStore } from '@/stores'
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import thorui from '@/components/common/tui-clipboard/tui-clipboard.js'
import FileCard from '@/components/file-card/file-card.vue'

const query = defineProps<{
  uuid: string
  id: string
}>()

const { voteDetail, voteResult, currentVoteItem } = storeToRefs(useVoteStore())
const { fetchVoteDetailAction, fetchVoteResultAction } = useVoteStore()

// 房屋全称
const fullHouseName = computed(() => {
  if (currentVoteItem.value) {
    const { floor_name, unit_name, room_name } = currentVoteItem.value
    return `${floor_name}-${unit_name}-${room_name}`
  }
})

const isChecked = computed(() => {
  return (option: any) => {
    return voteResult.value?.item.some((item) => item?.option_id?.includes(String(option.id))) ?? false
  }
})

// 复制投票码
const copyCode = (code: string) => {
  thorui.getClipboardData(code, (res: boolean) => {
    if (res) {
      uni.showToast({
        title: '已复制',
        icon: 'success',
      })
    }
  })
}

onLoad(() => {
  fetchVoteDetailAction(query.uuid)
  fetchVoteResultAction(currentVoteItem.value!.pqbm)
})
</script>

<template>
  <view class="bg-white w-[100vw] h-[100vh]">
    <view class="vote-detail__card pt-12 h-50.5 bg-image font-700 text-lg text-white px-6 text-center">
      {{ voteDetail.title }}
    </view>
    <!-- 投票码 -->
    <view class="px-5 -mt-14">
      <image class="w-full h-7" mode="aspectFill" src="@/static/images/vote_result_line.png" />
      <view class="relative bg-[#FEF6F3] mx-1.5 -mt-2 pb-3">
        <image class="absolute right-3 top-0 size-14" mode="aspectFill" src="@/static/images/vote_signet.png" />
        <view class="flex items-center justify-center gap-x-1">
          <text class="font-500 text-2xl text-[#EB5A4D]">{{ currentVoteItem!.pqbm }}</text>
          <image
            class="size-4.5"
            mode="aspectFill"
            src="@/static/icons/copy.png"
            @tap="copyCode(currentVoteItem!.pqbm)"
          />
        </view>
        <view class="text-center">票权编码</view>
        <view class="px-5 mt-2 font-350 text-xs text-[#666]">
          *请您牢记您的投票码，我们将在投票结束后，通过投票码公布投票结果。
        </view>
      </view>
    </view>
    <!-- 房号 -->
    <view class="py-2 px-3.5 font-350 text-sm"> 房号：{{ fullHouseName }} </view>
    <view class="pb-2 px-3.5 font-350 text-sm"> 面积：{{ currentVoteItem!.area }}㎡ </view>
    <view class="placeholder-box" />
    <!-- 议题选项 -->
    <view class="px-3.5 pt-4">
      <view v-for="(item, index) in voteDetail.items" :key="item.id" class="mb-7">
        <view class="mb-2 text-[#EF4F3F] text-base text-center font-500"> 事项{{ index + 1 }}：{{ item.title }} </view>
        <view class="mt-4">
          <!-- 单选 -->
          <template v-if="item.type === VoteOptionsTypeEnum.SINGLE">
            <tui-radio-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label v-for="(option, i) in item.option_arr" :key="i">
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-radio :checked="isChecked(option)" disabled> </tui-radio>
                  </view>
                </tui-label>
              </view>
            </tui-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else>
            <tui-checkbox-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label v-for="(option, i) in item.option_arr" :key="i">
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-checkbox :checked="isChecked(option)" disabled> </tui-checkbox>
                  </view>
                </tui-label>
              </view>
            </tui-checkbox-group>
          </template>
        </view>
        <!--   附件-->
        <view v-if="item.file.length" class="mt-4 flex flex-col gap-y-2">
          <FileCard v-for="(file, i) in item.file" :key="i" :name="file.name" :url="file.url" />
        </view>
      </view>
    </view>
    <view class="placeholder-box"></view>

    <!--    签名-->
    <view class="relative p-3.5 h-50 bg-white">
      <view class="mb-3 text-base font-500">签名</view>
      <image :src="voteResult?.qz_image" class="w-full" mode="widthFix" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.vote-detail__card {
  background-image: url('@/static/images/vote_detail_bg.png');
}

.placeholder-box {
  @apply bg-neutral-100 h-2;
}

:deep(.tui-checkbox__input):has(.tui-checkbox__hidden) {
  border-radius: 0 !important;
}
:deep(.tui-checkbox__disabled) {
  opacity: 1 !important;
}
</style>
