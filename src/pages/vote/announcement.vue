<script lang="ts" setup>
import { debounce } from 'lodash-es'
import { formatDate } from '@/utils'
import { getResultTableHeaderApi, getVotePublicityApi } from '@/api/vote'

const query = defineProps<{
  uuid: string
}>()

const tableHeader = ref<any[]>([])
const tableData = ref<any[]>([])
const searchValue = ref('')

onReady(async () => {
  await fetchTableHeader()
  await fetchVotePublicityData()
})

const fetchTableHeader = async () => {
  const res = await getResultTableHeaderApi(query.uuid)
  tableHeader.value = res.map((item: any, index: number) => ({
    type: 1,
    width: 60 * item.lable.length,
    fixed: index === 0 ? 'left' : index === res.length - 1 ? 'right' : false,
    label: item.lable,
    prop: item.key,
  }))
}

const fetchVotePublicityData = async () => {
  const { data } = await getVotePublicityApi({
    uuid: query.uuid,
    pqbm: searchValue.value,
  })

  tableData.value = data.map((item: any) => {
    const key = tableHeader.value.at(-1).prop

    return {
      ...item,
      [key]: formatDate(item[key]),
    }
  })
}

const handleInput = debounce((value: string) => {
  searchValue.value = value
  fetchVotePublicityData()
}, 500)

const handleConfirm = () => {
  fetchVotePublicityData()
}
</script>

<template>
  <view class="announcement-wrapper bg-no-repeat bg-[length:100%_428rpx]">
    <view class="pt-4 text-white font-500 text-base text-center">结果明细公开查询</view>
    <view
      class="my-4 flex px-2 items-center text-sm h-9 mx-9 rounded-full gap-x-2"
      style="background: rgba(255, 255, 255, 0.3); backdrop-filter: blur(4px)"
    >
      <tui-icon name="search-2" size="16" color="#960000" />
      <tui-input
        v-model="searchValue"
        placeholder="搜索票权编码"
        :border-bottom="false"
        size="28"
        placeholder-style="color: #960000"
        confirm-type="search"
        @input="handleInput"
        @confirm="handleConfirm"
      ></tui-input>
    </view>

    <view class="bg-white rounded-t-5 overflow-hidden">
      <tui-virtual-table
        :border="false"
        :header="tableHeader"
        :height="0"
        :hor-border="false"
        ellipsis
        :full="tableHeader.length < 4"
        :item-height="100"
        text-size="24"
        :table-data="tableData"
        fixed
        header-bg-color="#f6f6f6"
        stripe
        stripe-color="#F6F6F6"
      ></tui-virtual-table>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.announcement-wrapper {
  background-image: url('@/static/images/vote_announcement_bg.png');
}

:deep(.tui-input__wrap) {
  padding: 0 !important;
  height: 100% !important;
  background-color: transparent !important;
}
</style>
