<script lang="ts" setup>
import { useVoteStore } from '@/stores'

const { voteParams, touristVoteParams } = storeToRefs(useVoteStore())

const signatureRef = ref<any>(null)

// 清除签名
const clearSignature = () => {
  signatureRef.value?.clear()
}
// 保存签名图片
const confirmSignature = () => {
  signatureRef.value.canvasToTempFilePath({
    success: (result: any) => {
      if (!result.isEmpty) {
        voteParams.value.qz_image = result.tempFilePath
        touristVoteParams.value.qz_image = result.tempFilePath
        uni.navigateBack()
      }
    },
  })
}
</script>

<template>
  <view class="h-[100vh] flex">
    <view class="relative w-15 bg-white">
      <view class="rotate-90 absolute bottom-32 right-0 left-0">
        <tui-form-button color="#333" height="68rpx" link plain width="140rpx" @click="clearSignature">
          清除
        </tui-form-button>
      </view>
      <view class="rotate-90 absolute bottom-10 right-0 left-0">
        <tui-form-button :size="28" height="68rpx" width="140rpx" @click="confirmSignature">确认</tui-form-button>
      </view>
    </view>
    <view class="flex-1">
      <l-signature
        ref="signatureRef"
        :maxLineWidth="20"
        background-color="#f5f5f5"
        disableScroll
        landscape
        openSmooth
        style="width: 100%; height: 100%"
      ></l-signature>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
