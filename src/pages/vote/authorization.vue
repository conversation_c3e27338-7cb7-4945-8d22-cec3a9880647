<script lang="ts" setup>
import { getAssetsImages, saveImageToAlbum } from '@/utils'
import { getAuthCodeApi, submitHouseProofApi } from '@/api/tourist'
import Upload from '@/components/upload/upload.vue'
import tuiPoster from '@/components/thorui/tui-poster/tui-poster.vue'

const query = defineProps<{
  id: string
  tourist_topic_id: string
}>()

const {
  systemScreenInfo: { width, height },
} = useSystemRectInfo()

const tabData = ['业主人脸识别', '补充资料']
const activeTab = ref(0)

const communityName = ref('')
const userName = ref('')
const authCode = ref('')

const authForm = reactive<any>({
  tourist_topic_id: query.tourist_topic_id,
  images: [],
})

const sfz_1 = ref([])
const sfz_2 = ref([])

const posterRef = ref<InstanceType<typeof tuiPoster> | null>(null)

const basicPosterData = computed(() => {
  return [
    {
      type: 'rect',
      style: {
        left: 0,
        top: 0,
        width: width,
        height: height,
        backgroundColor: '#ffffff',
      },
    },
    {
      type: 'image',
      src: getAssetsImages('auth_bg.png'),
      style: {
        left: 0,
        top: 0,
        width: width,
        height: height,
      },
    },
    {
      type: 'text',
      text: communityName.value,
      style: {
        left: width / 2,
        top: 160,
        fontSize: 22,
        color: '#fff',
        textAlign: 'center',
        fontWeight: 'bold',
      },
    },
    {
      type: 'text',
      text: '业主大会投票结果授权',
      style: {
        left: width / 2,
        top: 200,
        fontSize: 22,
        color: '#fff',
        textAlign: 'center',
        fontWeight: 'bold',
      },
    },
    {
      type: 'image',
      src: getAssetsImages('qrcode_bg.png'),
      style: {
        left: width / 2 - 147,
        top: 264,
        width: 294,
        height: 320,
      },
    },
    {
      type: 'image',
      src: authCode.value,
      style: {
        left: width / 2 - 96,
        top: 340,
        width: 192,
        height: 192,
      },
    },
    {
      type: 'image',
      src: getAssetsImages('透明嘉logo.jpg'),
      style: {
        left: width / 2 - 34,
        top: 230,
        width: 68,
        height: 68,
        borderRadius: 68,
      },
    },
    {
      type: 'text',
      text: userName.value,
      style: {
        left: width / 2,
        top: 320,
        fontSize: 18,
        color: '#333',
        textAlign: 'center',
        fontWeight: 'bold',
      },
    },
    {
      type: 'rect',
      style: {
        top: 620,
        left: width / 2 - 123,
        width: 246,
        height: 50,
        backgroundColor: '#fff',
        borderRadius: 50,
      },
    },
    {
      type: 'text',
      text: '长按识别二维码授权验证',
      style: {
        left: width / 2,
        top: 650,
        fontSize: 16,
        color: '#F55545',
        textAlign: 'center',
        fontWeight: 'bold',
      },
    },
  ]
})

onLoad(() => {
  fetchAuthCode()
})

const fetchAuthCode = async () => {
  const { qrcode, community } = await getAuthCodeApi(query.id)
  authCode.value = qrcode
  communityName.value = community
}

// 绘制海报
const drawImage = () => {
  uni.showLoading({
    title: '正在保存...',
  })
  posterRef.value?.draw(basicPosterData.value, async (filePath: any) => {
    saveImageToAlbum(filePath)
    uni.hideLoading()
  })
}

// 提交授权资料
const handleSubmit = async () => {
  const sfz = [...sfz_1.value, ...sfz_2.value]
  if (!sfz_1.value.length) {
    return uni.showToast({
      title: '请上传人像面',
      icon: 'none',
    })
  }
  if (!sfz_2.value.length) {
    return uni.showToast({
      title: '请上传国徽面',
      icon: 'none',
    })
  }
  if (!authForm.images.length) {
    uni.showToast({
      title: '请上传产权证明',
      icon: 'none',
    })
    return
  }
  await submitHouseProofApi({
    tourist_topic_id: authForm.tourist_topic_id,
    images: {
      house_img: authForm.images,
      sfz_img: sfz,
    },
  })
  uni.showToast({
    title: '提交成功',
    icon: 'none',
  })
  uni.navigateTo({
    url: `/pages/index/index-tmj`,
  })
}
</script>

<template>
  <view class="wrapper size-full">
    <view class="flex items-center justify-center gap-x-5 text-lg font-500 pt-7">
      <view
        v-for="(tab, index) in tabData"
        :key="index"
        :class="[activeTab === index ? 'bg-white text-[#EB5A4D]' : 'bg-white/20 text-white']"
        class="w-35 h-11 rounded flex items-center justify-center"
        @click="activeTab = index"
      >
        {{ tab }}
      </view>
    </view>

    <view class="text-white text-5.5 font-500 text-center mt-7">
      <view>{{ communityName }}</view>
      <view>业主大会投票{{ activeTab === 0 ? '人脸识别' : '补充资料' }}授权</view>
    </view>
    <!-- 二维码 -->
    <view v-if="activeTab === 0" class="flex flex-col items-center justify-center mt-15">
      <view
        :style="{ backgroundImage: `url(${getAssetsImages('qrcode_bg.png')})` }"
        class="w-73.5 h-80 bg-image flex flex-col items-center"
      >
        <view>
          <image class="size-17 rounded-full -mt-8.5" mode="aspectFill" src="@/static/images/透明嘉logo.jpg" />
          <view class="text-lg text-center">{{ userName }}</view>
        </view>

        <view class="size-48 relative mt-4">
          <view
            class="absolute -top-1 -left-1 border-solid border-r-none border-b-none border-t-4 border-l-4 border-[#EF2A1F]/20 size-5"
          ></view>
          <view
            class="absolute -top-1 -right-1 border-solid border-r-4 border-l-none border-t-4 border-b-none border-[#EF2A1F]/20 size-5"
          ></view>
          <view
            class="absolute -bottom-1 -left-1 border-solid border-r-none border-b-4 border-l-4 border-t-none border-[#EF2A1F]/20 size-5"
          ></view>
          <view
            class="absolute -bottom-1 -right-1 border-solid border-r-4 border-l-none border-b-4 border-t-none border-[#EF2A1F]/20 size-5"
          ></view>

          <image :src="authCode" class="size-full" mode="aspectFill" />
        </view>

        <view class="mt-4">邀请房屋业主人脸识别授权</view>
      </view>
      <!-- 保存二维码 -->
      <view class="flex flex-col items-center mt-7.5">
        <image class="size-13 rounded-full" mode="aspectFill" src="@/static/images/save.png" @tap="drawImage" />
        <text class="text-sm text-white mt-2">下载二维码</text>
      </view>
    </view>

    <!-- 补充产权信息 -->
    <view v-if="activeTab === 1" class="mt-8 p-3.5">
      <view class="h-100 bg-white p-4">
        <tui-form>
          <tui-form-item
            :bottom-border="false"
            :label="`上传产权证明（${authForm.images.length}/3）`"
            :position="1"
            asterisk
            labelWidth="auto"
          >
            <template v-slot:row>
              <Upload v-model="authForm.images" :isSfz="false" :limit="3" :sfz_z="false" />
            </template>
          </tui-form-item>

          <tui-form-item :bottom-border="false" :label="`上传身份证`" :position="1" asterisk labelWidth="auto">
            <template v-slot:row>
              <view class="grid grid-cols-2 gap-x-5">
                <view class="flex items-center justify-center flex-col">
                  <Upload v-model="sfz_1" :isSfz="true" :limit="1" :sfz_z="true" />
                  <view class="text-#F66233 mt-2">上传人像面</view>
                </view>
                <view class="flex items-center justify-center flex-col">
                  <Upload v-model="sfz_2" :isSfz="true" :limit="1" :sfz_z="false" />
                  <view class="text-#F66233 mt-2">上传国徽面</view>
                </view>
              </view>
            </template>
          </tui-form-item>

          <view class="mt-40">
            <tui-form-button background="#F66233" height="92rpx" size="28" @click="handleSubmit">提交</tui-form-button>
          </view>
        </tui-form>
      </view>
    </view>
  </view>

  <tui-poster ref="posterRef" :height="height" :width="width"></tui-poster>
</template>

<style lang="scss" scoped>
.wrapper {
  background: url('@/static/images/auth_bg.png') no-repeat left top/100% 100%;
}
</style>
