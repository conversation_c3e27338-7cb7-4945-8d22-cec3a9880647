<script lang="ts" setup>
import { useVoteStore } from '@/stores'
import FooterButton from '@/components/footer-button/footer-button.vue'

const query = defineProps<{
  uuid: string
}>()

const { voteDetail } = storeToRefs(useVoteStore())
const { fetchVoteDetailAction } = useVoteStore()

// 倒计时
const countdown = computed(() => voteDetail.value?.e_time - useTimestamp().value / 1000)

const isAgree = ref(false)

const handleCheckAgree = () => {
  isAgree.value = !isAgree.value
}
// 确认参会
const handleConfirm = () => {
  uni.navigateTo({
    url: `/pages/vote/detail?uuid=${query.uuid}`,
  })
}

onLoad(() => {
  fetchVoteDetailAction(query.uuid)
})
</script>

<template>
  <view class="receive-wrapper pt-4 bg-no-repeat bg-center-top bg-[length:100%_618rpx]">
    <view class="p-3 mx-3.5 bg-white/25 rounded-lg backdrop-blur-sm">
      <view class="text-lg font-500 text-[#FFF8BB]">{{ voteDetail.title }}</view>
      <view class="text-white mt-2 flex items-center">
        <text>倒计时：</text>
        <tui-countdown
          :is-colon="false"
          :size="28"
          :time="countdown"
          background-color="transparent"
          border-color="transparent"
          colon-color="#fff"
          color="#fff"
          days
        ></tui-countdown>
      </view>
    </view>
    <view class="mt-3">
      <view class="px-2">
        <image class="w-full h-3.5" mode="aspectFill" src="@/static/images/vote_receive_line.png" />
      </view>
      <view class="relative -top-3 px-3.5 z-1">
        <view class="w-full h-3 bg-gradient-to-b from-[#FFD7CC] from-5% to-[#FFDCCC]/0 to-98%"></view>
        <view class="relative -top-0.5 pt-3.5 px-3 pb-8 bg-white rounded-b-lg z-3">
          <view class="mb-4.5 font-700 text-xl text-[#3D3D3D] text-center">票权信息</view>
          <view class="flex flex-col gap-y-4.5">
            <view v-for="(item, index) in voteDetail.items" :key="index" class="px-4 pb-4 bg-[#FDF6F3] rounded-lg">
              <view
                class="receive-item__title mb-2 h-7.5 font-700 leading-7.5 text-lg text-[#EF4629] text-center bg-image"
              >
                议题{{ index + 1 }}
              </view>
              <view class="mt-3.5 text-center text-base text-black font-500">{{ item.title }}</view>
            </view>
          </view>

          <view class="flex items-start gap-x-1.5 mt-10">
            <tui-checkbox :checked="isAgree" :scaleRatio="0.7" color="#FA483B" @tap="handleCheckAgree"> </tui-checkbox>
            <view class="text-xs text-black" @tap="handleCheckAgree">
              我已阅读上述所有相关条款，以下所投票为本人真实意愿表达
            </view>
          </view>
          <view class="mt-3 px-6 font-350 text-[#EF4F3F] text-xs"> *已确认参会未表决的业主，其投票权数计入多数票 </view>
        </view>
      </view>
    </view>
    <FooterButton :disabled="!isAgree" @click="handleConfirm">确认参会</FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.receive-wrapper {
  min-height: 100vh;
  background-color: #f5f5f5;
  background-image: url('@/static/images/vote_front_bg.png');
}

.receive-item {
  &__title {
    background-image: url('@/static/images/vote_receive_title.png');
  }
}
</style>
