<script lang="ts" setup>
import { useVoteStore } from '@/stores'
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import type { VoteMatterItem } from '@/api/vote/types'
import FooterButton from '@/components/footer-button/footer-button.vue'

const query = defineProps<{
  uuid: string
  id?: string
}>()

const { voteDetail, voteParams } = storeToRefs(useVoteStore())
const { submitVoteAction, fetchVoteDetailAction, resetVoteParamsAction } = useVoteStore()

// 是否显示加载动画
const showLoading = ref(false)
// 是否显示提示框
const showAlert = ref(false)

const isChecked = computed(() => {
  return (option: any) => {
    const item = voteParams.value.item.find((n) => n?.id === option.item_id)
    if (!item) return false
    return item.option_id.includes(option.id)
  }
})

const isDisabled = computed(() => {
  return (option: any) => {
    if (option.isLimit !== 1) return false
    const optionCount = voteParams.value.item[option.index]?.option_id.length
    if (!optionCount) return false
    return optionCount >= option.maxNumber
  }
})

// 是否签名
const isSign = computed(() => voteParams.value.qz_image)

// 选项点击
const handleOptionSelect = (option: any, type: 'radio' | 'checkbox') => {
  const selectOptionId = option.id
  const index = voteParams.value.item?.findIndex((voteItem) => voteItem?.id === option.item_id)

  const voteItem: VoteMatterItem = {
    id: option.item_id,
    type: option.type,
    option_id: [selectOptionId],
  }

  // 判断当前题目是否选择
  const isItemChecked = index !== -1

  // 当前题目没有选择，直接添加
  if (!isItemChecked) {
    return (voteParams.value.item![option.index] = voteItem)
  }

  // 当前题目已经选择过
  // 判断当前题目选项是否已选择
  const isOptionChecked = voteParams.value.item[index]?.option_id.includes(selectOptionId)
  // 单选
  if (type === 'radio') {
    // 如果已选择，则删除当前选项，否则替换
    voteParams.value.item[index] = isOptionChecked ? undefined : voteItem
  } else if (type === 'checkbox') {
    // 多选
    if (isOptionChecked) {
      const selectOptionIndex = voteParams.value.item[index]?.option_id.indexOf(selectOptionId)
      if (selectOptionIndex !== -1 && selectOptionIndex !== undefined) {
        voteParams.value.item[index]!.option_id.splice(selectOptionIndex, 1)
      }
      // 如果当前题目没有选项，则删除
      if (!voteParams.value.item[index]?.option_id.length) {
        voteParams.value.item[index] = undefined
      }
    } else {
      const optionCount = voteParams.value.item[index]?.option_id.length
      if (option.isLimit === 1 && optionCount && optionCount >= option.maxNumber) {
        return uni.showToast({
          title: `最多选择${option.maxNumber}个`,
          icon: 'none',
        })
      }

      voteParams.value.item[index]!.option_id.push(selectOptionId)
    }
  }
}

// 提交投票
const handleSubmitVote = async () => {
  // 检验所有议题是否已答
  const unAnsweredItems = voteDetail.value.items.filter((item: any) => {
    return !voteParams.value.item?.some((param) => param?.id === item.id)
  })

  if (unAnsweredItems.length) {
    const toastTitle = `请回答：议题 ${unAnsweredItems
      .map((q: any) => voteDetail.value.items.findIndex((item: any) => item.id === q.id) + 1)
      .join('、')}`

    uni.showToast({
      title: toastTitle,
      icon: 'none',
      duration: 3000,
    })
    return
  }

  // 校验每个议题的最少/最多选项数量（仅 is_limit === 1 时校验）
  for (let i = 0; i < voteDetail.value.items.length; i++) {
    const item = voteDetail.value.items[i]
    if (item.is_limit === 1) {
      const param = voteParams.value.item.find((p: any) => p.id === item.id)
      const selectedCount = param ? param.option_id.length : 0
      if (selectedCount < item.min_number) {
        return uni.showToast({
          title: `议题${i + 1} 最少选择${item.min_number}个选项，当前已选择${selectedCount}个`,
          icon: 'none',
          duration: 5000,
        })
      }
      if (selectedCount > item.max_number) {
        return uni.showToast({
          title: `议题${i + 1} 最多选择${item.max_number}个选项，当前已选择${selectedCount}个`,
          icon: 'none',
          duration: 5000,
        })
      }
    }
  }

  if (!isSign.value) return (showAlert.value = true)
  try {
    showLoading.value = true
    await submitVoteAction()
    uni.redirectTo({
      url: `/pages/vote/result?uuid=${query.uuid}`,
    })
  } finally {
    showLoading.value = false
  }
}
// 签名
const handleSignClick = () => {
  showAlert.value = false
  uni.navigateTo({
    url: './sign',
  })
}

const handleFileClick = () => {
  uni.navigateTo({
    url: `/pages/file-notice/index?topic_id=${voteDetail.value.id}`,
  })
}

onLoad(() => {
  fetchVoteDetailAction(query.uuid)
})

onUnload(() => {
  resetVoteParamsAction()
})
</script>

<template>
  <view class="min-h-[100vh] bg-neutral-100">
    <view class="vote-detail__card pt-12 h-47.5 bg-image font-700 text-lg text-[#FFF8BB] px-6 text-center">
      {{ voteDetail.title }}
    </view>
    <!-- 会议说明 -->
    <view class="pt-2 bg-white">
      <view class="flex justify-between px-3.5">
        <view class="flex items-center gap-x-2">
          <view class="w-1 h-4 bg-[#EB5A4D] rounded"></view>
          <view class="text-[#333] font-500 text-base">会议说明：</view>
        </view>
        <view class="flex items-center text-[#EB5A4D]" @click="handleFileClick">
          <text>文件公示</text>
          <tui-icon :size="20" color="#EB5A4D" name="arrowright" />
        </view>
      </view>
      <view class="px-3.5 py-3">
        <text class="font-350 text-justify">
          {{ voteDetail.explain }}
        </text>
      </view>
      <view class="px-3.5 pb-3.5 flex items-center justify-between font-350 text-[#999] text-xs">
        <text>发布时间：{{ voteDetail.created_at }}</text>
        <view class="flex items-center gap-x-1">
          <image class="size-4.5" mode="aspectFill" src="@/static/icons/free.png" />
          <text class="mt-0.5">投票进行中</text>
        </view>
      </view>
      <view class="p-3.5 bor-t font-350 text-xs">
        投票范围：{{ voteDetail.house_num }}户房屋，专有部分面积总和为{{ voteDetail.area }}㎡
      </view>
    </view>
    <view class="placeholder-box" />
    <!-- 议题选项 -->
    <view class="px-3.5 py-4 flex flex-col gap-y-7 bg-white">
      <view v-for="(item, index) in voteDetail.items" :key="index">
        <view class="mb-2 text-[#EF4F3F] text-base text-center font-500">
          议题{{ index + 1 }}：{{ item.title }}
          <text v-if="item.is_limit === 1" class="text-xs text-[#333] font-350">
            (最少选择{{ item.min_number }}个，最多选择{{ item.max_number }}个)
          </text>
        </view>
        <view class="mt-4">
          <!-- 单选 -->
          <template v-if="item.type === VoteOptionsTypeEnum.SINGLE">
            <tui-radio-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label
                  v-for="(option, i) in item.option_arr"
                  :key="i"
                  @tap="handleOptionSelect({ ...option, index, item_id: item.id, type: item.type }, 'radio')"
                >
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked({ ...option, item_id: item.id }) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-radio
                      :checked="isChecked({ ...option, item_id: item.id })"
                      @tap="handleOptionSelect({ ...option, index, item_id: item.id, type: item.type }, 'radio')"
                    >
                    </tui-radio>
                  </view>
                </tui-label>
              </view>
            </tui-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else>
            <tui-checkbox-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label
                  v-for="(option, i) in item.option_arr"
                  :key="i"
                  @tap="
                    handleOptionSelect(
                      {
                        ...option,
                        index,
                        item_id: item.id,
                        type: item.type,
                        minNumber: item.min_number,
                        maxNumber: item.max_number,
                        isLimit: item.is_limit,
                      },
                      'checkbox',
                    )
                  "
                >
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked({ ...option, item_id: item.id }) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-checkbox
                      :checked="isChecked({ ...option, item_id: item.id })"
                      :disabled="
                        isDisabled({
                          ...option,
                          item_id: item.id,
                          type: item.type,
                          index,
                          minNumber: item.min_number,
                          maxNumber: item.max_number,
                          isLimit: item.is_limit,
                        })
                      "
                      @tap="
                        handleOptionSelect(
                          {
                            ...option,
                            index,
                            item_id: item.id,
                            type: item.type,
                            minNumber: item.min_number,
                            maxNumber: item.max_number,
                            isLimit: item.is_limit,
                          },
                          'checkbox',
                        )
                      "
                    >
                    </tui-checkbox>
                  </view>
                </tui-label>
              </view>
            </tui-checkbox-group>
          </template>
        </view>
      </view>
    </view>
    <view class="placeholder-box"></view>
    <!--    签名-->
    <view v-if="voteParams.qz_image" class="relative p-3.5 bg-white">
      <view class="mb-3 text-base font-500">签名</view>
      <image :src="voteParams.qz_image" class="w-full" mode="widthFix" />
    </view>

    <view class="h-6 bg-neutral-100"></view>

    <FooterButton @click="handleSubmitVote"> 投票 </FooterButton>
  </view>

  <!--  加载动画-->
  <tui-loading v-if="showLoading" is-mask></tui-loading>

  <!--  签名弹窗-->
  <tui-modal :show="showAlert" custom>
    <view class="mb-5 text-center text-lg font-500">温馨提示</view>
    <view class="px-5 pb-5">
      <view class="mb-1 text-base font-500">
        尊敬的{{ voteDetail.community_name }}小区{{ voteDetail.full_house_name }}业主:
      </view>
      <view class="text-base">您在提交业主大会投票结果前，请先点击“去签名”完成电子签名</view>
    </view>
    <view class="flex h-14 bor-t">
      <tui-form-button color="#333" height="100%" link plain @click="showAlert = false">取消</tui-form-button>
      <view class="w-[1px] h-full bg-[#E7E7E7]" />
      <tui-form-button color="#EF4F3F" height="100%" link plain @click="handleSignClick">去签名</tui-form-button>
    </view>
  </tui-modal>
</template>

<style lang="scss" scoped>
.vote-detail__card {
  background-image: url('@/static/images/vote_detail_bg.png');
}

.placeholder-box {
  @apply bg-neutral-100 h-2;
}

:deep(.tui-modal-box) {
  padding: 40rpx 0 0 !important;
}

:deep(.tui-checkbox__input):has(.tui-checkbox__hidden) {
  border-radius: 0 !important;
}
</style>
