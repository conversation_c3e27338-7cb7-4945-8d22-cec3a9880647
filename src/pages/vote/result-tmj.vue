<script lang="ts" setup>
import { getAssetsImages } from '@/utils'
import { useUserStore } from '@/stores'
import { IsPassCheckEnum } from '@/api/user/types'
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import { VoteProgressEnum } from '@/api/ticket/types.ts'
import { confirmAuthApi, getTouristTopicDetailApi, getTouristTopicResultApi,getNewTouristTopicResultApi  } from '@/api/tourist'
import thorui from '@/components/common/tui-clipboard/tui-clipboard.js'
import FileCard from '@/components/file-card/file-card.vue'
import FooterButton from '@/components/footer-button/footer-button.vue'
import ModalSuccess from '@/components/modal-success/modal-success.vue'

const query = defineProps<{
  id: string // 议题id
  qrcodeId?: string // 小程序码携带的id
  isGolden?: 'true' | 'false' // 是否是金标用户
  token?: string // 用户token
  isFwtz?: 'true' | 'false' // 服务通知进来
  pqbm?: string // 票权编码
  original?: string // 结果页面  h5内部调过来的 为 1, 小程序分析和服务通知调过来的 为 2
  topic_id?: string // 结果页面  h5内部调过来的 为 1, 小程序分析和服务通知调过来的 为 2
}>()

const { userInfo, token: userToken } = storeToRefs(useUserStore())

// 授权弹框
const showAuth = ref(false)
const showAuthSuccess = ref(false)
const voteDetail = ref<any>({})
const voteResult = ref<any>({})

onLoad(async () => {
  if (query.token) {
    userToken.value = query.token
  }
  console.log(query,'-ss')
  await Promise.all([fetchVoteDetail(), fetchVoteResult()])
  // 需要补充授权弹框
  if (voteResult.value.is_upload === 1) {
    setTimeout(() => {
      showAuthSuccess.value = true
    }, 500)
  }
})

const isChecked = computed(() => {
  return (option: any) => {
    if (!voteResult.value?.item) return false
    return voteResult.value?.item.some((item: any) => item.option_id.includes(String(option.id))) ?? false
  }
})

// 补充授权
const handleAuthorization = () => {
  uni.navigateTo({
    url: `/pages/vote/authorization?id=${query.id}&tourist_topic_id=${voteResult.value.tourist_topic_id}`,
  })
}

// 获取投票详情
const fetchVoteDetail = async () => {
  voteDetail.value = await getTouristTopicDetailApi(query.topic_id || query.id || '')
  console.log('🚀 ~ fetchVoteDetail ~ voteDetail.value:', voteDetail.value)
}
// 获取投票结束
const fetchVoteResult = async () => {
  // original= 1, 小程序分析 进入 2
  if(query.original === '1') {
    voteResult.value = await getTouristTopicResultApi(query.id)
  }else {
    voteResult.value = await getNewTouristTopicResultApi(query.id)
  }
  console.log('🚀 ~ fetchVoteResult ~ voteResult.value:', voteResult.value)
}

// 复制投票码
const copyCode = (code: string) => {
  thorui.getClipboardData(code, (res: boolean) => {
    if (res) {
      uni.showToast({
        title: '已复制',
        icon: 'success',
      })
    }
  })
}

// 确定授权
const handleAuth = async () => {
  if (!query.qrcodeId) return
  const { is_success } = await confirmAuthApi(query.qrcodeId)
  if (is_success === 1) {
    showAuth.value = true
  } else {
    uni.showToast({
      title: '授权失败',
      icon: 'none',
    })
  }
}

const handleResult = () => {
  uni.navigateTo({
    url: `/pages/vote/announcement-tmj?id=${query.id}&uuid=${voteResult.value.pq?.uuid}&pqbm=${voteResult.value.pq?.pqbm}`,
  })
}

const handleConfirm = () => {
  uni.navigateTo({
    url: `/pages/index/index-tmj`,
  })
}
</script>

<template>
  <view class="bg-white w-[100vw] h-[100vh]">
    <view class="vote-detail__card pt-12 h-50.5 bg-image font-700 text-lg text-white px-6 text-center">
      {{ voteDetail.title }}
    </view>

    <!-- 服务通知显示投票编码 -->
    <view v-if="isFwtz && JSON.parse(isFwtz) && pqbm" class="px-5 -mt-14">
      <image class="w-full h-7" mode="aspectFill" src="@/static/images/vote_result_line.png" />
      <view class="relative bg-[#FEF6F3] mx-1.5 -mt-2 pb-3">
        <image class="absolute right-3 top-0 size-14" mode="aspectFill" src="@/static/images/vote_signet.png" />
        <view class="flex items-center justify-center gap-x-1">
          <text class="font-500 text-2xl text-[#EB5A4D]">{{ pqbm }}</text>
          <image class="size-4.5" mode="aspectFill" src="@/static/icons/copy.png" @tap="copyCode(pqbm)" />
        </view>
        <view class="text-center">票权编码</view>
        <view class="px-5 mt-2 font-350 text-xs text-[#666]">
          *请您牢记您的投票码，我们将在投票结束后，通过投票码公布投票结果。
        </view>
      </view>
    </view>

    <!-- 金标用户显示房屋信息 -->
    <view
      v-if="
        voteResult.community?.is_pass_check && voteResult.community?.is_pass_check === IsPassCheckEnum.金标 && !qrcodeId
      "
      class="p-4 bg-[#FDF6F3] rounded-lg border border-solid receive-item relative mx-6 -mt-16"
    >
      <image
        class="absolute right-5 top-1/2 -translate-y-1/2 size-14"
        mode="aspectFill"
        src="@/static/images/不动产权_icon.png"
      />
      <view class="flex items-center">
        <view class="flex flex-col gap-y-2">
          <view class="text-lg font-700 text-[#EF4629]">{{ voteResult.community?.name }}</view>
          <view class="text-base"> {{ voteResult.community?.address }} {{ voteResult.community?.area }}m² </view>
          <view class="flex items-center gap-x-1">
            <text class="text-base font-700">{{ userInfo?.name }}</text>
            <image :src="getAssetsImages(`业主1_icon.png`)" style="width: 130rpx" mode="widthFix" />
          </view>
        </view>
      </view>
    </view>

    <!-- 议题选项 -->
    <view class="px-3.5 pt-4">
      <view v-for="(item, index) in voteDetail.items" :key="item.id" class="mb-7">
        <view class="mb-2 text-[#EF4F3F] text-base text-center font-500"> 事项{{ index + 1 }}：{{ item.title }} </view>
        <view class="mt-4">
          <!-- 单选 -->
          <template v-if="item.type === VoteOptionsTypeEnum.SINGLE">
            <tui-radio-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label v-for="(option, i) in item.option_arr" :key="i">
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-radio :checked="isChecked(option)" disabled> </tui-radio>
                  </view>
                </tui-label>
              </view>
            </tui-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else>
            <tui-checkbox-group>
              <view class="flex flex-col gap-y-2.5">
                <tui-label v-for="(option, i) in item.option_arr" :key="i">
                  <view
                    :class="[
                      'flex items-center justify-between px-3 h-10 rounded',
                      isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                    ]"
                  >
                    <text>{{ option.content }}</text>
                    <tui-checkbox :checked="isChecked(option)" disabled> </tui-checkbox>
                  </view>
                </tui-label>
              </view>
            </tui-checkbox-group>
          </template>
        </view>
        <!--   附件-->
        <view v-if="item.file.length" class="mt-4 flex flex-col gap-y-2">
          <FileCard v-for="(file, i) in item.file" :key="i" :name="file.name" :url="file.url" />
        </view>
      </view>
    </view>
    <view class="placeholder-box"></view>

    <!--    签名-->
    <view class="relative p-3.5 h-50 bg-white">
      <view class="mb-3 text-base font-500">签名</view>
      <image :src="voteResult?.qz_image" class="w-full" mode="widthFix" />
    </view>
  </view>

  <FooterButton v-if="voteDetail.is_start === VoteProgressEnum.FINISHED" text="查看结果" @click="handleResult" />

  <FooterButton v-else-if="isGolden && JSON.parse(isGolden)" text="确定授权" @click="handleAuth" />

  <ModalSuccess
    v-model="showAuthSuccess"
    title="您的投票已完成"
    content="投票已记录，需要业主来授权认证，投票才生效"
    confirm-text="补充授权"
    :show-cancel="false"
    @confirm="handleAuthorization"
  />

  <!-- 授权弹框 -->
  <ModalSuccess
    v-model="showAuth"
    title="授权成功"
    :content="`您名下房屋：${voteResult.community?.name}-${voteResult.community?.address}已参与业主大会投票 敬请关注后续结果`"
    confirm-text="确定"
    :show-cancel="false"
    @confirm="handleConfirm"
  >
  </ModalSuccess>
</template>

<style lang="scss" scoped>
.vote-detail__card {
  background-image: url('@/static/images/vote_detail_bg.png');
}

.placeholder-box {
  @apply bg-neutral-100 h-2;
}

.receive-item {
  border-color: #ff801f !important;
  box-shadow: 0 4px 10px 0 rgba(255, 97, 44, 0.37);
  &__title {
    background-image: url('@/static/images/vote_receive_title.png');
  }
}

:deep(.tui-checkbox__input):has(.tui-checkbox__hidden) {
  border-radius: 0 !important;
}
:deep(.tui-checkbox__disabled) {
  opacity: 1 !important;
}
</style>
