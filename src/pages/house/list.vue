<script lang="ts" setup>
import { getAssetsImages } from '@/utils'
import { IsPassCheckEnum } from '@/api/user/types'
import { getRoomListApi } from '@/api/tourist'
import FooterButton from '@/components/footer-button/footer-button.vue'

const roomList = ref<any[]>([])

onLoad(() => {
  fetchRoomList()
})

const fetchRoomList = async () => {
  const { data } = await getRoomListApi({ page: 1, limit: 1000 })
  roomList.value = data
}

const handleConfirm = () => {
  uni.navigateTo({
    url: '/pages/index/index-tmj',
  })
}
</script>

<template>
  <view class="receive-wrapper bg-no-repeat bg-center-top bg-[length:100%_416rpx]">
    <view class="pt-45">
      <view class="px-2">
        <image class="w-full h-3.5" mode="aspectFill" src="@/static/images/vote_receive_line.png" />
      </view>
      <view class="relative -top-3 px-3.5 z-1">
        <view class="w-full h-3 bg-gradient-to-b from-[#FFD7CC] from-5% to-[#FFDCCC]/0 to-98%"></view>
        <view class="relative -top-0.5 pt-3.5 px-3 pb-9 bg-white rounded-b-lg z-3">
          <view class="mb-4.5 font-700 text-xl text-[#3D3D3D] text-center">房屋信息</view>
          <view class="flex flex-col gap-y-3.5">
            <view
              v-for="(item, index) in roomList"
              :key="index"
              class="px-4 pb-4 bg-[#FDF6F3] rounded-lg border border-solid receive-item relative"
            >
              <image
                class="absolute right-5 top-1/2 -translate-y-1/2 size-14"
                mode="aspectFill"
                src="@/static/images/不动产权_icon.png"
              />

              <view
                class="receive-item__title mb-2 h-7.5 font-700 leading-7.5 text-lg text-[#EF4629] text-center bg-image"
              >
                房屋{{ String(index + 1).padStart(2, '0') }}
              </view>
              <view class="flex items-center">
                <view class="flex-1 flex flex-col gap-y-2">
                  <view class="text-lg font-700 text-[#EF4629]">{{ item.community }}</view>
                  <view class="text-base"> {{ item.address }} </view>
                  <view class="flex items-center gap-x-1">
                    <text class="text-base font-700">{{ item.name }}</text>
                    <image
                      v-if="item?.is_pass_check"
                      :src="
                        getAssetsImages(
                          `${item.is_pass_check === IsPassCheckEnum.金标 ? '业主1_icon' : '业主2_icon'}.png`,
                        )
                      "
                      style="width: 130rpx"
                      mode="widthFix"
                    />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <FooterButton @click="handleConfirm">确认</FooterButton>
  </view>
</template>

<style lang="scss" scoped>
.receive-wrapper {
  min-height: 100vh;
  background-color: #fff7f6;
  background-image: url('@/static/images/vote_receive_bg.png');
}

.receive-item {
  border-color: #ff801f !important;
  box-shadow: 0 4px 10px 0 rgba(255, 97, 44, 0.37);
  &__title {
    background-image: url('@/static/images/vote_receive_title.png');
  }
}
</style>
