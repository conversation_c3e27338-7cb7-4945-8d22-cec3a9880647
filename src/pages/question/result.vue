<script lang="ts" setup>
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import { getQuestionExplainApi, getQuestionnaireApi } from '@/api/question'
import { useUserStore } from '@/stores'

const query = defineProps<{
  phone?: string
  question_id?: string
}>()

const { loginQuestionAction } = useUserStore()

const questionDetail = ref<any>()
// 问卷说明
const questionExplain = ref<any>({})

const isChecked = computed(() => (option: any) => !!option.created_at)

// 获取问卷题目及记录
const fetchQuestionnaireData = async () => {
  questionDetail.value = await getQuestionnaireApi()
}
// 获取问卷说明
const fetchQuestionExplainData = async () => {
  const res = await getQuestionExplainApi()
  questionExplain.value = {
    explain: res.explain,
    created_at: res.created_at,
  }
}

onLoad(async () => {
  if (query.phone && query.question_id) {
    await loginQuestionAction({
      mobile: query.phone,
      question_id: query.question_id,
    })
  }
  fetchQuestionnaireData()
  fetchQuestionExplainData()
})
</script>

<template>
  <view class="min-h-[100vh] bg-neutral-100">
    <!-- 会议说明 -->
    <view class="pt-2 bg-white">
      <view class="flex justify-between px-3.5">
        <view class="flex items-center gap-x-2">
          <view class="w-1 h-4 bg-[#EB5A4D] rounded"></view>
          <view class="text-[#333] font-500 text-base">前言：</view>
        </view>
      </view>
      <view class="px-3.5 py-3">
        <text class="font-350 text-justify">
          {{ questionExplain.explain }}
        </text>
      </view>
      <view class="px-3.5 pb-3.5 flex items-center justify-between font-350 text-[#999] text-xs">
        <text>发布时间：{{ questionExplain.created_at }}</text>
      </view>
    </view>
    <view class="placeholder-box" />
    <!-- 选项 -->
    <view class="px-3.5 py-4 flex flex-col gap-y-7 bg-white">
      <view v-for="(options, index) in questionDetail" :key="index">
        <view class="mb-2 text-base font-500">
          {{ index + 1 }}.
          <text class="text-[#EB5A4D]">({{ options[0].type === VoteOptionsTypeEnum.SINGLE ? '单选' : '多选' }})</text>
          {{ options[0].title }}
        </view>
        <view class="mt-4">
          <!-- 单选 -->
          <template v-if="options[0].type === VoteOptionsTypeEnum.SINGLE">
            <tui-radio-group>
              <view class="flex flex-col gap-y-2.5">
                <template v-for="(option, i) in options" :key="i">
                  <tui-label v-if="option.option_type !== 2">
                    <view
                      :class="[
                        'flex items-center justify-between px-3 h-10 rounded',
                        isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                      ]"
                    >
                      <text>{{ option.content }}</text>
                      <tui-radio :checked="isChecked(option)" disabled> </tui-radio>
                    </view>
                  </tui-label>

                  <!-- 文本框 -->
                  <view v-if="option.option_type === 2">
                    <view class="my-2.5">{{ option.content }}</view>
                    <tui-textarea
                      disabled
                      :model-value="option.option_str"
                      background-color="#f7f7f7"
                      auto-height
                      placeholder="请输入"
                      height="40rpx"
                      :border-top="false"
                    />
                  </view>
                </template>
              </view>
            </tui-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else>
            <tui-checkbox-group>
              <view class="flex flex-col gap-y-2.5">
                <template v-for="(option, i) in options" :key="i">
                  <tui-label v-if="option.option_type !== 2">
                    <view
                      :class="[
                        'flex items-center justify-between px-3 py-2.5 rounded',
                        isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                      ]"
                    >
                      <text>{{ option.content }}</text>
                      <tui-checkbox :checked="isChecked(option)" disabled> </tui-checkbox>
                    </view>
                  </tui-label>

                  <!-- 文本框 -->
                  <view v-if="option.option_type === 2">
                    <view class="my-2.5">{{ option.content }}</view>
                    <tui-textarea
                      disabled
                      :model-value="option.option_str"
                      background-color="#f7f7f7"
                      auto-height
                      placeholder="请输入"
                      height="40rpx"
                      :border-top="false"
                    />
                  </view>
                </template>
              </view>
            </tui-checkbox-group>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.tui-checkbox__input):has(.tui-checkbox__hidden) {
  border-radius: 0 !important;
}
:deep(.tui-checkbox__disabled) {
  opacity: 1 !important;
}
</style>
