<script lang="ts" setup>
import FooterButton from '@/components/footer-button/footer-button.vue'
import ModalSuccess from '@/components/modal-success/modal-success.vue'
import { useUserStore } from '@/stores'
import { VoteOptionsTypeEnum } from '@/api/vote/types.ts'
import { getQuestionExplainApi, getQuestionnaireApi, submitQuestionApi } from '@/api/question'

const query = defineProps<{
  phone?: string
  question_id?: string
}>()

const { loginQuestionAction } = useUserStore()

// 是否显示加载动画
const showLoading = ref(false)
// 是否显示提交成功
const showSuccess = ref(false)

const questionDetail = ref<any>()
// 问卷说明
const questionExplain = ref<any>({})
// 提交问卷参数
const questionParams = ref<any[]>([])

const isChecked = computed(() => {
  return (option: any) => {
    const item = questionParams.value.find((item: any) => item.items_id === option.items_id)
    if (!item) return false
    return item.option_id.includes(option.option_id)
  }
})

// 获取问卷题目及记录
const fetchQuestionnaireData = async () => {
  questionDetail.value = await getQuestionnaireApi()
}
// 获取问卷说明
const fetchQuestionExplainData = async () => {
  const res = await getQuestionExplainApi()
  questionExplain.value = {
    explain: res.explain,
    created_at: res.created_at,
  }
}

// 文本框改变
const handleTextareaChange = (event: any, options: any) => {
  const option = options[0]

  const index = questionParams.value.findIndex((item) => item.items_id === option.items_id)
  // 获取所有文本框选项
  const hasOptionStrArr = options.filter((item: any) => item.option_type === 2)
  const optionStrParamsArr = hasOptionStrArr.map((item: any) => ({
    option_id: item.option_id,
    str: event,
  }))

  // 判断是否已存在
  const isChecked = index !== -1

  if (!isChecked) {
    questionParams.value.push({
      items_id: option.items_id,
      option_type: option.option_type,
      option_str: optionStrParamsArr,
      option_id: [],
    })
  } else {
    const item = questionParams.value[index]
    questionParams.value[index] = {
      items_id: item.items_id,
      option_type: item.option_type,
      option_str: optionStrParamsArr,
      option_id: item.option_id,
    }
  }
}
// 选项点击
const handleOptionSelect = (option: any, type: 'radio' | 'checkbox') => {
  const selectOptionId = option.option_id
  const index = questionParams.value.findIndex((item) => item.items_id === option.items_id)

  const questionItem = {
    items_id: option.items_id,
    option_type: option.option_type,
    option_str: [],
    option_id: [selectOptionId],
  }

  // 判断当前题目是否选择
  const isItemChecked = index !== -1

  // 当前题目没有选择，直接添加
  if (!isItemChecked) {
    return questionParams.value.push(questionItem)
  }

  // 当前题目已经选择过
  // 判断当前题目选项是否已选择
  const isOptionChecked = questionParams.value[index].option_id.includes(selectOptionId)
  // 单选
  if (type === 'radio') {
    // 如果已选择，则删除当前选项，否则替换
    isOptionChecked ? questionParams.value.splice(index, 1) : (questionParams.value[index] = questionItem)
  } else {
    // 多选
    if (isOptionChecked) {
      const selectOptionIndex = questionParams.value[index].option_id.indexOf(selectOptionId)
      questionParams.value[index].option_id.splice(selectOptionIndex, 1)
      // 如果当前题目没有选项，则删除
      if (!questionParams.value[index].option_id.length) {
        questionParams.value.splice(index, 1)
      }
    } else {
      questionParams.value[index].option_id.push(selectOptionId)
    }
  }
}

// 提交问卷
const handleSubmit = async () => {
  // 校验所有题目是否已答
  const unAnsweredQuestions = questionDetail.value.filter((options: any[]) => {
    const question = options[0]
    // 跳过文本类型的题目
    if (question.option_type === 2) return false
    return !questionParams.value.some((param) => param.items_id === question.items_id)
  })

  if (unAnsweredQuestions.length) {
    const toastTitle = `请回答：第 ${unAnsweredQuestions
      .map((q: any[]) => questionDetail.value.findIndex((item: any[]) => item[0].items_id === q[0].items_id) + 1)
      .join('、')} 题`

    uni.showToast({
      title: toastTitle,
      icon: 'none',
      duration: 3000,
    })
    return
  }

  try {
    showLoading.value = true
    await submitQuestionApi(questionParams.value)
    showSuccess.value = true
  } finally {
    showLoading.value = false
  }
}

onLoad(async () => {
  if (query.phone && query.question_id) {
    await loginQuestionAction({
      mobile: query.phone,
      question_id: query.question_id,
    })
  }
  fetchQuestionnaireData()
  fetchQuestionExplainData()
})
</script>

<template>
  <view class="min-h-[100vh] bg-neutral-100">
    <!-- 会议说明 -->
    <view class="pt-2 bg-white">
      <view class="flex justify-between px-3.5">
        <view class="flex items-center gap-x-2">
          <view class="w-1 h-4 bg-[#EB5A4D] rounded"></view>
          <view class="text-[#333] font-500 text-base">前言：</view>
        </view>
      </view>
      <view class="px-3.5 py-3">
        <text class="font-350 text-justify">
          {{ questionExplain.explain }}
        </text>
      </view>
      <view class="px-3.5 pb-3.5 flex items-center justify-between font-350 text-[#999] text-xs">
        <text>发布时间：{{ questionExplain.created_at }}</text>
      </view>
    </view>
    <view class="placeholder-box" />
    <!-- 选项 -->
    <view class="px-3.5 py-4 flex flex-col gap-y-7 bg-white">
      <view v-for="(options, index) in questionDetail" :key="index">
        <view class="mb-2 text-base font-500">
          {{ index + 1 }}.
          <text class="text-[#EB5A4D]">({{ options[0].type === VoteOptionsTypeEnum.SINGLE ? '单选' : '多选' }})</text>
          {{ options[0].title }}
        </view>
        <view class="mt-4">
          <!-- 单选 -->
          <template v-if="options[0].type === VoteOptionsTypeEnum.SINGLE">
            <tui-radio-group>
              <view class="flex flex-col gap-y-2.5">
                <template v-for="(option, i) in options" :key="i">
                  <tui-label v-if="option.option_type !== 2" @tap="handleOptionSelect(option, 'radio')">
                    <view
                      :class="[
                        'flex items-center justify-between px-3 h-10 rounded',
                        isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                      ]"
                    >
                      <text>{{ option.content }}</text>
                      <tui-radio :checked="isChecked(option)" @tap="handleOptionSelect(option, 'radio')"> </tui-radio>
                    </view>
                  </tui-label>

                  <!-- 文本框 -->
                  <view v-if="option.option_type === 2">
                    <view class="my-2.5">{{ option.content }}</view>
                    <tui-textarea
                      background-color="#f7f7f7"
                      auto-height
                      placeholder="请输入"
                      height="40rpx"
                      :border-top="false"
                      @input="(e: any) => handleTextareaChange(e, options)"
                    />
                  </view>
                </template>
              </view>
            </tui-radio-group>
          </template>
          <!-- 多选 -->
          <template v-else>
            <tui-checkbox-group>
              <view class="flex flex-col gap-y-2.5">
                <template v-for="(option, i) in options" :key="i">
                  <tui-label v-if="option.option_type !== 2" @tap="handleOptionSelect(option, 'checkbox')">
                    <view
                      :class="[
                        'flex items-center justify-between px-3 py-2.5 rounded',
                        isChecked(option) ? 'bg-[#EB5A4D]/10 text-[#EB5A4D]' : 'bg-neutral-100',
                      ]"
                    >
                      <text>{{ option.content }}</text>
                      <tui-checkbox :checked="isChecked(option)" @tap="handleOptionSelect(option, 'checkbox')">
                      </tui-checkbox>
                    </view>
                  </tui-label>

                  <!-- 文本框 -->
                  <view v-if="option.option_type === 2">
                    <view class="my-2.5">{{ option.content }}</view>
                    <tui-textarea
                      background-color="#f7f7f7"
                      auto-height
                      placeholder="请输入"
                      height="40rpx"
                      :border-top="false"
                      @input="(e: any) => handleTextareaChange(e, options)"
                    />
                  </view>
                </template>
              </view>
            </tui-checkbox-group>
          </template>
        </view>
      </view>
    </view>
    <FooterButton @click="handleSubmit"> 提交 </FooterButton>
  </view>

  <!--  加载动画-->
  <tui-loading v-if="showLoading" is-mask></tui-loading>

  <ModalSuccess v-model="showSuccess" title="提交成功">
    <template #content>
      <view class="px-15"> 感谢您在百忙之中参与本次问卷调查！ </view>
    </template>
    <template #bottom>
      <tui-button type="primary" width="100%" height="80rpx" size="28" @click="$t.route('./result')">
        查看您的问卷
      </tui-button>
    </template>
  </ModalSuccess>
</template>

<style lang="scss" scoped>
:deep(.tui-checkbox__input):has(.tui-checkbox__hidden) {
  border-radius: 0 !important;
}
</style>
