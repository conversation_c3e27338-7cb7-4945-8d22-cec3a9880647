import { useUserStore } from '@/stores'
import { authLogin } from '@/utils/auth'

const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  invoke({ url }: { url: string }) {
    const token = useUserStore().token
    const path = url.split('?')[0]

    // 白名单，不需要登录的页面
    const whiteList: string[] = ['/pages/login/index', '/pages/login/worker', '/pages/index/index','/pages/file-notice/index','/pages/empty/empty']
    const notNeedLogin = whiteList.includes(path)
    // 不需要登录的页面，直接返回 true
    if (notNeedLogin) return true

    // 需要登录的页面，没有 token 则跳转到登录页面
    if (!token && !notNeedLogin) {
      authLogin()
      return false
    }

    return true
  },
}

export const routeInterceptor = {
  install() {
    const list = ['navigateTo', 'reLaunch', 'redirectTo', 'switchTab']
    list.forEach((item) => {
      uni.addInterceptor(item, navigateToInterceptor)
    })
  },
}
