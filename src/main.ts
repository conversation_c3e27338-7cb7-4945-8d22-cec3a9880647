import 'uno.css'
import '@/static/styles/index.scss'
import './config/z-paging.ts'

import propsConfig from './components/thorui/tui-config/index.js'

import t from './utils/install.ts'
import { routeInterceptor } from '@/interceptors'

import { createSSRApp } from 'vue'
import App from './App.vue'
import pinia from './stores'

export function createApp() {
  const app = createSSRApp(App)
  app.use(pinia)
  app.use(t)
  app.use(routeInterceptor)
  uni.$tui = propsConfig
  return {
    app,
  }
}
