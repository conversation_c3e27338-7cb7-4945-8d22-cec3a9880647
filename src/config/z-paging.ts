// z-paging插件全局配置
uni.$zp = {
  config: {
    // 配置分页默认pageSize为15
    'default-page-size': 15,
    // 配置空数据图默认描述文字
    'empty-view-text': '暂无数据',
    // 是否开启底部安全区域适配
    'safe-area-inset-bottom': true,
    // 当没有更多数据且分页内容未超出z-paging时是否隐藏没有更多数据的view
    'hide-no-more-inside': true,
    // 滑动到底部"没有更多"文字
    'loading-more-no-more-text': 'THE END',
    // 列表刷新时自动显示下拉刷新view
    'show-refresher-when-reload': true,
    // 是否显示最后更新时间
    'show-refresher-update-time': true,
    // 控制是否出现滚动条
    'show-scrollbar': false,
    // 空数据图描述文字样式
    'empty-view-text-style': {
      fontSize: '28rpx',
      color: '#666',
    },
  },
}
