/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs')
const path = require('path')
const { exec } = require('child_process')

// Windows: HBuilderX安装目录根目录, cli.exe
// MacOSX
// 正式版 /Applications/HBuilderX.app/Contents/MacOS/cli
// Alpha版 /Applications/HBuilderX-Alpha.app/Contents/MacOS/cli

// cli publish --platform mp-weixin --project 项目名称 --upload true --appid 小程序appid --description 发布描述 --version 发布版本 --privatekey 小程序上传密钥文件

const { version } = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf-8'))

const config = {
  platform: 'mp-weixin',
  project: 'uniapp-vue3',
  upload: true,
  appid: 'wx2e7c35e603c40727',
  description: '测试cli发布版本',
  version: version || '1.0.0',
  privatekey: './key/private.wx2e7c35e603c40727.key',
  isAlpha: false,
  cli: '/Applications/HBuilderX.app/Contents/MacOS/cli',
}

const cmd = `${config.cli} publish --platform ${config.platform} --project ${config.project} --upload ${config.upload} --appid ${config.appid} --description "${config.description}" --version ${config.version} --privatekey "${path.join(__dirname, config.privatekey)}"`

exec(cmd, (error, stdout, stderr) => {
  if (error) {
    console.log('🚀 ~ exec ~ error:', error)
    return
  }
  console.log('🚀 ~ exec ~ stdout:', stdout)
  console.log('🚀 ~ exec ~ stderr:', stderr)
})
