{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/env.d.ts", "src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.jsx", "src/**/*.vue", "auto-imports.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "#/*": ["./typings/*"]}, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "@uni-helper/uni-app-types", "z-paging/types", "node"]}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-app-types/volar-plugin"]}}