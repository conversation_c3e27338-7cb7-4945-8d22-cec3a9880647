{"name": "session-h5", "version": "1.1.0", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build": "uni build", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint . --fix", "format": "prettier --write src/", "publish:mp-weixin": "node scripts/publish.js"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-components": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-h5": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4040520250107001", "@uni-helper/uni-network": "^0.20.0", "@vueuse/core": "^12.3.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "pdfjs-dist": "^5.3.93", "pinia": "2.3.0", "pinia-plugin-persistedstate": "4.2.0", "qs": "^6.13.1", "vue": "^3.5.13", "vue-i18n": "^11.0.1", "z-paging": "^2.8.4"}, "devDependencies": {"@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4040520250107001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4040520250107001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4040520250107001", "@esbuild/darwin-x64": "0.21.5", "@iconify/json": "^2.2.293", "@rollup/rollup-darwin-x64": "^4.28.1", "@tsconfig/node22": "^22.0.0", "@types/crypto-js": "^4.2.2", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.5", "@types/qs": "^6.9.17", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/unocss-preset-uni": "^0.2.10", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.2.0", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "prettier": "^3.3.3", "sass": "^1.83.1", "typescript": "^5.7.3", "unocss": "^0.65.4", "unplugin-auto-import": "^0.19.0", "vite": "^6.0.7", "vue-tsc": "^2.2.0"}, "resolutions": {"esbuild": "0.21.5"}}