import path from 'path'
import { copyFile } from 'fs'
import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import AutoImport from 'unplugin-auto-import/vite'

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  const {
    VITE_APP_PROXY,
    VITE_APP_PROXY_PREFIX,
    VITE_SERVER_PORT,
    VITE_SERVER_BASEURL,
    VITE_SHOW_SOURCEMAP,
    VITE_DELETE_CONSOLE,
    VITE_APP_PUBLIC_BASE,
  } = loadEnv(mode, path.resolve(process.cwd(), 'env'))

  const UnoCSS = await import('unocss/vite').then((i) => i.default)

  return {
    envDir: './env',
    base: VITE_APP_PUBLIC_BASE,
    plugins: [
      uni(),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],
        imports: ['vue', 'uni-app', 'pinia', '@vueuse/core'],
        dirs: ['src/hooks'],
      }),
      UnoCSS(),
      {
        name: 'html-transform',
        transformIndexHtml(html: string) {
          const version = process.env.NODE_ENV === 'production' ? process.env.BUILD_TIME : ''
          return html.replace(/<\/head>/, `<script>window.APP_VERSION = "${version}";</script></head>`)
        },
      },
      {
        name: 'copy-pdf-worker',
        buildStart() {
          copyFile('node_modules/pdfjs-dist/build/pdf.worker.mjs', 'src/static/js/pdf.worker.min.js', (err) => {
            if (err) console.error('Error copying PDF worker:', err)
          })
        },
      },
    ],
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    build: {
      sourcemap: VITE_SHOW_SOURCEMAP === 'true',
      target: 'es6',
      terserOptions: {
        compress: {
          drop_console: VITE_DELETE_CONSOLE === 'true',
          drop_debugger: true,
        },
      },
      rollupOptions: {
        output: {
          entryFileNames: `assets/[name].[hash].js`,
          chunkFileNames: `assets/[name].[hash].js`,
          assetFileNames: `assets/[name].[hash].[ext]`,
        },
      },
    },
    server: {
      open: true,
      host: '0.0.0.0',
      hmr: true,
      port: Number.parseInt(VITE_SERVER_PORT, 10),
      proxy: JSON.parse(VITE_APP_PROXY)
        ? {
            [VITE_APP_PROXY_PREFIX]: {
              target: VITE_SERVER_BASEURL,
              changeOrigin: true,
              rewrite: (path: string) => path.replace(new RegExp(`^${VITE_APP_PROXY_PREFIX}`), ''),
            },
            '/adminapi': {
              target: VITE_SERVER_BASEURL,
              changeOrigin: true,
              rewrite: (path: string) => path.replace(new RegExp(`^${VITE_APP_PROXY_PREFIX}`), ''),
            },
          }
        : undefined,
    },
  }
})
