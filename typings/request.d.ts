export interface HttpResult<T = any> {
  code: number
  msg: string
  status: number
  data: T
}

export interface PageParams {
  page?: number
  limit?: number
}

export interface PageResult<T = any> {
  total: number
  data: T[]
}

export interface FileParams {
  name: string
  url: string
}

export interface UploadFileResult {
  name: string
  src: string
  filepath: string
  full_filepath: string
  size: number
  date: string
}
