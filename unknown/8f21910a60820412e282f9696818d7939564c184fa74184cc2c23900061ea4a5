import type { UploadFileResult } from '#/request'
import type { UnConfig } from '@uni-helper/uni-network'
import { request } from '@/utils/request'

export enum Apis {
  UPLOAD_FILE = '/adminapi/common/upload',
  FILE_QRCODE = '/api/filesQrcode/detail',
}

/**
 * @description: 上传文件
 * @param config
 */
export const uploadFileApi = (config: UnConfig) => request.upload<UploadFileResult>(Apis.UPLOAD_FILE, config)

/**
 * @description: 文件公示
 * @param data
 * @param data.id
 * @param data.topic_id
 */
export const getFileInfoApi = (data: any) => request.post(Apis.FILE_QRCODE, data)
