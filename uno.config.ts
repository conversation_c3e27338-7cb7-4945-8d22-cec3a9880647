import { defineConfig, presetIcons, transformerDirectives, transformerVariantGroup } from 'unocss'
import { presetUni } from '@uni-helper/unocss-preset-uni'

export default defineConfig({
  presets: [
    presetUni({
      attributify: {
        prefixedOnly: true,
      },
    }),
    presetIcons({
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  shortcuts: [
    ['center', 'flex justify-center items-center'],
    ['bg-image', 'bg-no-repeat bg-center-top bg-cover'],
    [/^bor-(.*)$/, ([, c]) => `border-${c}-[0.5px] border-${c}-solid border-[#EBEBEB]`],
    [/^circle-(.*)$/, ([, c]) => `size-${c} rounded-full`],
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  theme: {},
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  ],
})
